@echo off
chcp 65001 >nul
title mitmproxy Web界面安装器

echo ========================================
echo    mitmproxy Web界面自动安装器
echo ========================================
echo.

echo [步骤 1/4] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python，请先安装Python 3.7+
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

echo.
echo [步骤 2/4] 安装mitmproxy...
pip install mitmproxy
if %errorlevel% neq 0 (
    echo ❌ mitmproxy安装失败
    pause
    exit /b 1
)
echo ✅ mitmproxy安装成功

echo.
echo [步骤 3/4] 创建E盘目录结构...
if not exist "E:\mitmproxy-setup" mkdir "E:\mitmproxy-setup"
if not exist "E:\mitmproxy-setup\scripts" mkdir "E:\mitmproxy-setup\scripts"
if not exist "E:\mitmproxy-setup\certs" mkdir "E:\mitmproxy-setup\certs"
if not exist "E:\mitmproxy-setup\logs" mkdir "E:\mitmproxy-setup\logs"

echo ✅ 目录结构创建完成

echo.
echo [步骤 4/4] 创建启动脚本...

echo @echo off > "E:\mitmproxy-setup\start-web.bat"
echo chcp 65001 ^>nul >> "E:\mitmproxy-setup\start-web.bat"
echo title mitmproxy Web Interface >> "E:\mitmproxy-setup\start-web.bat"
echo echo ======================================== >> "E:\mitmproxy-setup\start-web.bat"
echo echo    mitmproxy Web Visualization >> "E:\mitmproxy-setup\start-web.bat"
echo echo ======================================== >> "E:\mitmproxy-setup\start-web.bat"
echo echo. >> "E:\mitmproxy-setup\start-web.bat"
echo echo Starting mitmproxy Web Interface... >> "E:\mitmproxy-setup\start-web.bat"
echo echo Proxy Address: %%COMPUTERNAME%%:8080 >> "E:\mitmproxy-setup\start-web.bat"
echo echo Web Interface: http://127.0.0.1:8081 >> "E:\mitmproxy-setup\start-web.bat"
echo echo Working Directory: E:\mitmproxy-setup >> "E:\mitmproxy-setup\start-web.bat"
echo echo. >> "E:\mitmproxy-setup\start-web.bat"
echo echo Usage Instructions: >> "E:\mitmproxy-setup\start-web.bat"
echo echo 1. Connect phone to same WiFi as computer >> "E:\mitmproxy-setup\start-web.bat"
echo echo 2. Set phone HTTP proxy: %%COMPUTERNAME%%:8080 >> "E:\mitmproxy-setup\start-web.bat"
echo echo 3. Visit mitm.it on phone to download certificate >> "E:\mitmproxy-setup\start-web.bat"
echo echo 4. Install certificate and start capturing >> "E:\mitmproxy-setup\start-web.bat"
echo echo. >> "E:\mitmproxy-setup\start-web.bat"
echo cd /d "E:\mitmproxy-setup" >> "E:\mitmproxy-setup\start-web.bat"
echo mitmweb --web-host 0.0.0.0 --web-port 8081 --listen-port 8080 --set confdir=E:\mitmproxy-setup --set web_open_browser=false >> "E:\mitmproxy-setup\start-web.bat"

echo ✅ 启动脚本创建完成

echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 📁 安装目录: E:\mitmproxy-setup
echo 🚀 启动方式: 运行 E:\mitmproxy-setup\start-web.bat
echo 🌐 Web界面: http://127.0.0.1:8081
echo 📱 代理设置: %COMPUTERNAME%:8080
echo.
echo 📋 下一步操作:
echo 1. 运行 E:\mitmproxy-setup\start-web.bat
echo 2. 手机设置代理为电脑IP:8080
echo 3. 访问 mitm.it 下载证书
echo 4. 在手机上安装证书
echo.
echo Start mitmproxy now? (y/n)
set /p start_now=
if /i "%start_now%"=="y" (
    echo Starting...
    start "" "E:\mitmproxy-setup\start-web.bat"
    echo Web interface will open at: http://127.0.0.1:8081
)
echo.
pause
