#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API指纹识别工具 - 确定真实短信服务商
通过API响应特征、错误码、消息格式等识别上游供应商
"""

import requests
import json
import time
import hashlib
from datetime import datetime
import re

class APIFingerprintDetector:
    def __init__(self, base_url="http://**************:8394"):
        self.base_url = base_url
        self.session = requests.Session()
        self.fingerprints = {}
        self.vendor_signatures = self._load_vendor_signatures()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        })
    
    def _load_vendor_signatures(self):
        """加载已知服务商的API特征签名"""
        return {
            "yunpian": {
                "error_codes": ["1", "2", "3", "4", "5"],
                "response_fields": ["code", "msg", "data"],
                "api_style": "restful",
                "auth_method": "apikey",
                "typical_errors": {
                    "1": "参数错误",
                    "2": "账户余额不足", 
                    "3": "手机号格式错误"
                }
            },
            "chuanglan": {
                "error_codes": ["0", "101", "102", "103"],
                "response_fields": ["code", "message", "msgId"],
                "api_style": "soap_like",
                "auth_method": "username_password",
                "typical_errors": {
                    "101": "账号密码错误",
                    "102": "余额不足",
                    "103": "手机号码格式错误"
                }
            },
            "ronglian": {
                "error_codes": ["000000", "160001", "160002"],
                "response_fields": ["statusCode", "statusMsg", "templateSMS"],
                "api_style": "restful",
                "auth_method": "token",
                "typical_errors": {
                    "160001": "账户信息异常",
                    "160002": "账户余额不足"
                }
            },
            "submail": {
                "error_codes": ["success", "error"],
                "response_fields": ["status", "send_id", "fee"],
                "api_style": "restful", 
                "auth_method": "appid_signature",
                "typical_errors": {
                    "10010": "用户名或密码错误",
                    "10011": "余额不足"
                }
            },
            "emay": {
                "error_codes": ["0", "1", "2", "3"],
                "response_fields": ["result", "msgid", "custid"],
                "api_style": "custom",
                "auth_method": "cdkey",
                "typical_errors": {
                    "1": "账号密码错误",
                    "2": "余额不足"
                }
            }
        }
    
    def _log(self, message):
        """日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def collect_login_fingerprint(self, username="test", password="test", captcha="1234"):
        """收集登录接口的指纹特征"""
        self._log("🔍 收集登录接口指纹...")
        
        fingerprint = {
            "endpoint": "/login",
            "method": "POST",
            "timestamp": datetime.now().isoformat()
        }
        
        # 测试登录接口
        login_data = {
            'account': username,
            'password': password,
            'code': captcha,
            'remembermeclient': 'on'
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/login",
                data=login_data,
                timeout=10
            )
            
            fingerprint.update({
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content_type": response.headers.get('Content-Type', ''),
                "content_length": len(response.content),
                "response_time": response.elapsed.total_seconds()
            })
            
            # 分析响应内容
            try:
                json_response = response.json()
                fingerprint["response_format"] = "json"
                fingerprint["json_fields"] = list(json_response.keys())
                fingerprint["response_structure"] = self._analyze_json_structure(json_response)
            except:
                fingerprint["response_format"] = "html/text"
                fingerprint["content_sample"] = response.text[:200]
            
            self.fingerprints["login"] = fingerprint
            self._log(f"✅ 登录接口指纹收集完成")
            
        except Exception as e:
            self._log(f"❌ 登录接口测试失败: {str(e)}")
            fingerprint["error"] = str(e)
    
    def collect_sms_fingerprint(self, phone="13800138000", content="测试"):
        """收集短信发送接口的指纹特征"""
        self._log("🔍 收集短信发送接口指纹...")
        
        # 测试多个可能的端点
        endpoints = [
            "/api/sms/send",
            "/api/send", 
            "/sms/send",
            "/send",
            "/sendSms"
        ]
        
        sms_data_variants = [
            # JSON格式
            {
                "phone": phone,
                "content": content,
                "message": content,
                "text": content
            },
            # 表单格式
            {
                "mobile": phone,
                "msg": content,
                "content": content
            }
        ]
        
        for endpoint in endpoints:
            for i, data in enumerate(sms_data_variants):
                fingerprint_key = f"sms_{endpoint.replace('/', '_')}_{i}"
                fingerprint = {
                    "endpoint": endpoint,
                    "method": "POST",
                    "data_format": "json" if i == 0 else "form",
                    "timestamp": datetime.now().isoformat()
                }
                
                try:
                    if i == 0:  # JSON
                        response = self.session.post(
                            f"{self.base_url}{endpoint}",
                            json=data,
                            headers={'Content-Type': 'application/json'},
                            timeout=10
                        )
                    else:  # Form
                        response = self.session.post(
                            f"{self.base_url}{endpoint}",
                            data=data,
                            timeout=10
                        )
                    
                    fingerprint.update({
                        "status_code": response.status_code,
                        "headers": dict(response.headers),
                        "content_type": response.headers.get('Content-Type', ''),
                        "response_time": response.elapsed.total_seconds()
                    })
                    
                    # 分析响应
                    try:
                        json_response = response.json()
                        fingerprint["response_format"] = "json"
                        fingerprint["json_fields"] = list(json_response.keys())
                        fingerprint["response_structure"] = self._analyze_json_structure(json_response)
                        
                        # 提取错误码
                        error_code = self._extract_error_code(json_response)
                        if error_code:
                            fingerprint["error_code"] = error_code
                            
                    except:
                        fingerprint["response_format"] = "html/text"
                        fingerprint["content_sample"] = response.text[:200]
                    
                    self.fingerprints[fingerprint_key] = fingerprint
                    
                except Exception as e:
                    fingerprint["error"] = str(e)
                    self.fingerprints[fingerprint_key] = fingerprint
    
    def collect_error_fingerprints(self):
        """收集错误响应的指纹特征"""
        self._log("🔍 收集错误响应指纹...")
        
        # 故意触发各种错误
        error_tests = [
            {
                "name": "invalid_phone",
                "endpoint": "/api/sms/send",
                "data": {"phone": "invalid", "content": "test"}
            },
            {
                "name": "empty_content", 
                "endpoint": "/api/sms/send",
                "data": {"phone": "13800138000", "content": ""}
            },
            {
                "name": "long_content",
                "endpoint": "/api/sms/send", 
                "data": {"phone": "13800138000", "content": "x" * 1000}
            },
            {
                "name": "invalid_endpoint",
                "endpoint": "/api/invalid",
                "data": {"test": "test"}
            }
        ]
        
        for test in error_tests:
            fingerprint = {
                "test_type": test["name"],
                "endpoint": test["endpoint"],
                "timestamp": datetime.now().isoformat()
            }
            
            try:
                response = self.session.post(
                    f"{self.base_url}{test['endpoint']}",
                    json=test["data"],
                    timeout=10
                )
                
                fingerprint.update({
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "response_time": response.elapsed.total_seconds()
                })
                
                try:
                    json_response = response.json()
                    fingerprint["response_format"] = "json"
                    fingerprint["error_response"] = json_response
                    
                    # 提取错误信息
                    error_code = self._extract_error_code(json_response)
                    error_message = self._extract_error_message(json_response)
                    
                    if error_code:
                        fingerprint["error_code"] = error_code
                    if error_message:
                        fingerprint["error_message"] = error_message
                        
                except:
                    fingerprint["response_format"] = "html/text"
                    fingerprint["content_sample"] = response.text[:200]
                
                self.fingerprints[f"error_{test['name']}"] = fingerprint
                
            except Exception as e:
                fingerprint["error"] = str(e)
                self.fingerprints[f"error_{test['name']}"] = fingerprint
    
    def _analyze_json_structure(self, json_data):
        """分析JSON响应结构"""
        if isinstance(json_data, dict):
            return {
                "type": "object",
                "keys": list(json_data.keys()),
                "nested": any(isinstance(v, (dict, list)) for v in json_data.values())
            }
        elif isinstance(json_data, list):
            return {
                "type": "array", 
                "length": len(json_data),
                "item_type": type(json_data[0]).__name__ if json_data else "unknown"
            }
        else:
            return {"type": type(json_data).__name__}
    
    def _extract_error_code(self, response):
        """从响应中提取错误码"""
        possible_fields = ["code", "error_code", "status", "statusCode", "result", "errno"]
        
        for field in possible_fields:
            if field in response:
                return response[field]
        return None
    
    def _extract_error_message(self, response):
        """从响应中提取错误信息"""
        possible_fields = ["message", "msg", "error", "statusMsg", "error_msg", "desc"]
        
        for field in possible_fields:
            if field in response:
                return response[field]
        return None
    
    def analyze_vendor_match(self):
        """分析与已知服务商的匹配度"""
        self._log("🔍 分析服务商匹配度...")
        
        matches = {}
        
        for vendor_name, signature in self.vendor_signatures.items():
            score = 0
            details = []
            
            # 检查响应字段匹配
            for fingerprint_key, fingerprint in self.fingerprints.items():
                if "json_fields" in fingerprint:
                    field_matches = set(fingerprint["json_fields"]) & set(signature["response_fields"])
                    if field_matches:
                        score += len(field_matches) * 10
                        details.append(f"响应字段匹配: {field_matches}")
                
                # 检查错误码匹配
                if "error_code" in fingerprint:
                    error_code = str(fingerprint["error_code"])
                    if error_code in signature["error_codes"]:
                        score += 20
                        details.append(f"错误码匹配: {error_code}")
                
                # 检查错误信息匹配
                if "error_message" in fingerprint:
                    error_msg = fingerprint["error_message"]
                    for code, msg in signature.get("typical_errors", {}).items():
                        if msg in error_msg or error_msg in msg:
                            score += 15
                            details.append(f"错误信息匹配: {msg}")
            
            matches[vendor_name] = {
                "score": score,
                "details": details,
                "confidence": min(score / 100, 1.0)  # 转换为0-1的置信度
            }
        
        # 排序并返回最匹配的服务商
        sorted_matches = sorted(matches.items(), key=lambda x: x[1]["score"], reverse=True)
        
        return sorted_matches
    
    def generate_report(self):
        """生成完整的指纹分析报告"""
        self._log("📊 生成指纹分析报告...")
        
        vendor_matches = self.analyze_vendor_match()
        
        report = {
            "analysis_time": datetime.now().isoformat(),
            "target_platform": self.base_url,
            "fingerprints_collected": len(self.fingerprints),
            "fingerprint_data": self.fingerprints,
            "vendor_analysis": vendor_matches,
            "top_match": vendor_matches[0] if vendor_matches else None,
            "recommendations": self._generate_recommendations(vendor_matches)
        }
        
        return report
    
    def _generate_recommendations(self, vendor_matches):
        """生成对接建议"""
        if not vendor_matches:
            return ["无法确定服务商，建议联系平台方获取官方文档"]
        
        top_match = vendor_matches[0]
        vendor_name = top_match[0]
        confidence = top_match[1]["confidence"]
        
        recommendations = []
        
        if confidence > 0.7:
            recommendations.append(f"高置信度匹配 {vendor_name}，建议按照 {vendor_name} 的API规范进行对接")
        elif confidence > 0.4:
            recommendations.append(f"中等置信度匹配 {vendor_name}，建议进一步验证API特征")
        else:
            recommendations.append("匹配度较低，建议通过实际测试确认API规范")
        
        recommendations.extend([
            "建议申请测试账号进行实际API调用验证",
            "收集更多错误响应样本以提高识别准确度",
            "分析实际短信发送后的状态报告格式"
        ])
        
        return recommendations

def main():
    """主函数"""
    print("=" * 60)
    print("🕵️ API指纹识别工具 - 确定真实短信服务商")
    print("=" * 60)
    
    detector = APIFingerprintDetector()
    
    # 收集各种指纹
    detector.collect_login_fingerprint()
    detector.collect_sms_fingerprint()
    detector.collect_error_fingerprints()
    
    # 生成报告
    report = detector.generate_report()
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 指纹分析报告")
    print("=" * 60)
    
    print(f"收集到 {report['fingerprints_collected']} 个API指纹")
    
    if report['top_match']:
        top_vendor = report['top_match'][0]
        confidence = report['top_match'][1]['confidence']
        print(f"\n🎯 最匹配的服务商: {top_vendor}")
        print(f"置信度: {confidence:.2%}")
        print(f"匹配详情: {report['top_match'][1]['details']}")
    
    print(f"\n💡 对接建议:")
    for rec in report['recommendations']:
        print(f"  • {rec}")
    
    # 保存详细报告
    with open('api_fingerprint_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: api_fingerprint_report.json")
    print("=" * 60)

if __name__ == "__main__":
    main()
