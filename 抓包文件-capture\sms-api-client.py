#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
短信平台API客户端 - 基于逆向分析
目标平台: http://**************:8394
"""

import requests
import json
import time
from datetime import datetime
import base64
from urllib.parse import urljoin

class SMSPlatformClient:
    def __init__(self, base_url="http://**************:8394"):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = None
        self.session_id = None
        
        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'X-Requested-With': 'XMLHttpRequest'
        })
    
    def _make_url(self, endpoint):
        """构造完整URL"""
        return urljoin(self.base_url, endpoint)
    
    def _log(self, message):
        """日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {message}")
    
    def test_connection(self):
        """测试连接"""
        try:
            response = self.session.get(self._make_url('/loginBlue.html'), timeout=10)
            if response.status_code == 200:
                self._log("✅ 连接成功")
                return True
            else:
                self._log(f"❌ 连接失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            self._log(f"❌ 连接异常: {str(e)}")
            return False
    
    def get_captcha(self, save_path="captcha.png"):
        """获取验证码"""
        try:
            response = self.session.get(self._make_url('/code.aspx'), timeout=10)
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                self._log(f"✅ 验证码已保存到: {save_path}")
                return True
            else:
                self._log(f"❌ 获取验证码失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            self._log(f"❌ 获取验证码异常: {str(e)}")
            return False
    
    def login(self, username, password, captcha):
        """用户登录"""
        try:
            # 尝试多种可能的登录端点
            login_endpoints = ['/login', '/auth', '/signin', '/api/login']
            
            login_data = {
                'account': username,
                'password': password,
                'code': captcha,
                'remembermeclient': 'on'
            }
            
            for endpoint in login_endpoints:
                try:
                    self._log(f"🔍 尝试登录端点: {endpoint}")
                    
                    # 尝试POST表单提交
                    response = self.session.post(
                        self._make_url(endpoint),
                        data=login_data,
                        headers={'Content-Type': 'application/x-www-form-urlencoded'},
                        timeout=10
                    )
                    
                    self._log(f"响应状态: {response.status_code}")
                    self._log(f"响应内容: {response.text[:200]}...")
                    
                    if response.status_code == 200:
                        # 尝试解析JSON响应
                        try:
                            result = response.json()
                            if result.get('success') or result.get('code') == 200:
                                self.token = result.get('data', {}).get('token')
                                self._log("✅ 登录成功")
                                return True
                        except:
                            # 如果不是JSON，检查是否重定向到主页
                            if 'dashboard' in response.text or 'main' in response.text:
                                self._log("✅ 登录成功 (检测到页面跳转)")
                                return True
                    
                except Exception as e:
                    self._log(f"❌ 端点 {endpoint} 登录失败: {str(e)}")
                    continue
            
            self._log("❌ 所有登录端点都失败")
            return False
            
        except Exception as e:
            self._log(f"❌ 登录异常: {str(e)}")
            return False
    
    def send_sms(self, phone, content, sign="【测试签名】", template_id=None):
        """发送短信"""
        try:
            # 尝试多种可能的发送端点
            send_endpoints = [
                '/api/sms/send',
                '/api/send',
                '/sms/send',
                '/send',
                '/sendSms',
                '/api/message/send'
            ]
            
            sms_data = {
                'phone': phone,
                'content': content,
                'message': content,
                'text': content,
                'sign': sign,
                'signature': sign,
                'send_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'timestamp': int(time.time())
            }
            
            if template_id:
                sms_data['template_id'] = template_id
                sms_data['templateId'] = template_id
            
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            if self.token:
                headers['Authorization'] = f'Bearer {self.token}'
            
            for endpoint in send_endpoints:
                try:
                    self._log(f"🔍 尝试发送端点: {endpoint}")
                    
                    # 尝试JSON格式
                    response = self.session.post(
                        self._make_url(endpoint),
                        json=sms_data,
                        headers=headers,
                        timeout=10
                    )
                    
                    self._log(f"响应状态: {response.status_code}")
                    self._log(f"响应内容: {response.text[:200]}...")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if result.get('code') == 200 or result.get('success'):
                                self._log("✅ 短信发送成功")
                                return result
                        except:
                            if 'success' in response.text.lower():
                                self._log("✅ 短信发送成功 (文本响应)")
                                return {'success': True, 'response': response.text}
                    
                    # 如果JSON失败，尝试表单提交
                    response = self.session.post(
                        self._make_url(endpoint),
                        data=sms_data,
                        headers={'Content-Type': 'application/x-www-form-urlencoded'},
                        timeout=10
                    )
                    
                    if response.status_code == 200 and 'success' in response.text.lower():
                        self._log("✅ 短信发送成功 (表单提交)")
                        return {'success': True, 'response': response.text}
                        
                except Exception as e:
                    self._log(f"❌ 端点 {endpoint} 发送失败: {str(e)}")
                    continue
            
            self._log("❌ 所有发送端点都失败")
            return None
            
        except Exception as e:
            self._log(f"❌ 发送短信异常: {str(e)}")
            return None
    
    def query_status(self, msg_id):
        """查询发送状态"""
        try:
            status_endpoints = [
                f'/api/sms/status?msg_id={msg_id}',
                f'/api/status?id={msg_id}',
                f'/sms/status?msg_id={msg_id}',
                f'/query?msg_id={msg_id}'
            ]
            
            headers = {}
            if self.token:
                headers['Authorization'] = f'Bearer {self.token}'
            
            for endpoint in status_endpoints:
                try:
                    response = self.session.get(
                        self._make_url(endpoint),
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        try:
                            return response.json()
                        except:
                            return {'response': response.text}
                            
                except Exception as e:
                    continue
            
            return None
            
        except Exception as e:
            self._log(f"❌ 查询状态异常: {str(e)}")
            return None
    
    def get_balance(self):
        """查询账户余额"""
        try:
            balance_endpoints = [
                '/api/account/balance',
                '/api/balance',
                '/account/balance',
                '/balance'
            ]
            
            headers = {}
            if self.token:
                headers['Authorization'] = f'Bearer {self.token}'
            
            for endpoint in balance_endpoints:
                try:
                    response = self.session.get(
                        self._make_url(endpoint),
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        try:
                            return response.json()
                        except:
                            return {'response': response.text}
                            
                except Exception as e:
                    continue
            
            return None
            
        except Exception as e:
            self._log(f"❌ 查询余额异常: {str(e)}")
            return None

def main():
    """主函数 - 交互式测试"""
    print("=" * 60)
    print("🎯 短信平台API测试工具")
    print("目标: http://**************:8394")
    print("=" * 60)
    
    client = SMSPlatformClient()
    
    # 测试连接
    print("\n1. 测试连接...")
    if not client.test_connection():
        print("❌ 无法连接到服务器，请检查网络")
        return
    
    # 获取验证码
    print("\n2. 获取验证码...")
    if client.get_captcha():
        print("✅ 验证码已保存，请查看 captcha.png")
    
    # 登录测试
    print("\n3. 登录测试...")
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()
    captcha = input("请输入验证码: ").strip()
    
    if client.login(username, password, captcha):
        print("✅ 登录成功")
        
        # 发送短信测试
        print("\n4. 发送短信测试...")
        phone = input("请输入手机号: ").strip()
        content = input("请输入短信内容: ").strip()
        
        result = client.send_sms(phone, content)
        if result:
            print("✅ 短信发送测试完成")
            print(f"结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print("❌ 短信发送失败")
        
        # 查询余额
        print("\n5. 查询余额...")
        balance = client.get_balance()
        if balance:
            print(f"余额信息: {json.dumps(balance, ensure_ascii=False, indent=2)}")
        else:
            print("❌ 查询余额失败")
    
    else:
        print("❌ 登录失败")
    
    print("\n" + "=" * 60)
    print("🎯 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
