@echo off
chcp 65001 >nul
echo ============================================================
echo 🕵️ API指纹识别工具 - 确定真实短信服务商
echo ============================================================
echo.

set BASE_URL=http://**************:8394
set REPORT_FILE=api_fingerprint_report.txt

echo 开始时间: %date% %time% > %REPORT_FILE%
echo 目标平台: %BASE_URL% >> %REPORT_FILE%
echo. >> %REPORT_FILE%

echo [%time%] 🔍 测试登录页面...
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n服务器: %%{remote_ip}\n" -o login_page.html "%BASE_URL%/loginBlue.html"
echo. >> %REPORT_FILE%
echo === 登录页面测试 === >> %REPORT_FILE%
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" "%BASE_URL%/loginBlue.html" >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

echo [%time%] 🔍 测试验证码接口...
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" -o captcha.jpg "%BASE_URL%/code.aspx"
echo. >> %REPORT_FILE%
echo === 验证码接口测试 === >> %REPORT_FILE%
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" "%BASE_URL%/code.aspx" >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

echo [%time%] 🔍 测试登录接口...
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" -X POST -d "account=test&password=test&code=1234&remembermeclient=on" "%BASE_URL%/login"
echo. >> %REPORT_FILE%
echo === 登录接口测试 === >> %REPORT_FILE%
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" -X POST -d "account=test&password=test&code=1234&remembermeclient=on" "%BASE_URL%/login" >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

echo [%time%] 🔍 测试可能的API端点...

echo === API端点扫描 === >> %REPORT_FILE%

set endpoints=/api/sms/send /api/send /sms/send /send /sendSms /api/login /api/user/login /api/account/balance /api/sms/query

for %%e in (%endpoints%) do (
    echo [%time%] 测试端点: %%e
    echo. >> %REPORT_FILE%
    echo --- 端点: %%e --- >> %REPORT_FILE%
    
    REM 测试JSON格式
    echo JSON格式测试: >> %REPORT_FILE%
    curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" -X POST -H "Content-Type: application/json" -d "{\"phone\":\"***********\",\"content\":\"test\"}" "%BASE_URL%%%e" >> %REPORT_FILE% 2>&1
    echo. >> %REPORT_FILE%
    
    REM 测试表单格式
    echo 表单格式测试: >> %REPORT_FILE%
    curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" -X POST -d "phone=***********&content=test&mobile=***********&msg=test" "%BASE_URL%%%e" >> %REPORT_FILE% 2>&1
    echo. >> %REPORT_FILE%
    
    timeout /t 1 /nobreak >nul
)

echo [%time%] 🔍 测试错误响应...
echo === 错误响应测试 === >> %REPORT_FILE%

echo. >> %REPORT_FILE%
echo --- 无效手机号测试 --- >> %REPORT_FILE%
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" -X POST -H "Content-Type: application/json" -d "{\"phone\":\"invalid\",\"content\":\"test\"}" "%BASE_URL%/api/sms/send" >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

echo --- 空内容测试 --- >> %REPORT_FILE%
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" -X POST -H "Content-Type: application/json" -d "{\"phone\":\"***********\",\"content\":\"\"}" "%BASE_URL%/api/sms/send" >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

echo --- 无参数测试 --- >> %REPORT_FILE%
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" -X POST -H "Content-Type: application/json" -d "{}" "%BASE_URL%/api/sms/send" >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

echo --- 不存在的端点测试 --- >> %REPORT_FILE%
curl -s -w "HTTP状态码: %%{http_code}\n内容类型: %%{content_type}\n响应时间: %%{time_total}s\n" -X POST "%BASE_URL%/api/nonexistent" >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

echo [%time%] 🔍 分析服务器头部信息...
echo === 服务器头部信息 === >> %REPORT_FILE%
curl -s -I "%BASE_URL%/loginBlue.html" >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

echo [%time%] 📊 生成分析报告...

echo.
echo ============================================================
echo 📊 API指纹分析结果
echo ============================================================

echo.
echo 🌐 基础信息:
echo   目标平台: %BASE_URL%
echo   分析时间: %date% %time%

echo.
echo 📄 详细报告已保存到: %REPORT_FILE%

echo.
echo 🔍 关键发现:
findstr /C:"HTTP状态码: 200" %REPORT_FILE% >nul
if %errorlevel%==0 (
    echo   ✅ 发现可访问的端点
) else (
    echo   ❌ 未发现200状态码响应
)

findstr /C:"application/json" %REPORT_FILE% >nul
if %errorlevel%==0 (
    echo   ✅ 发现JSON格式响应
) else (
    echo   ❌ 未发现JSON响应
)

findstr /C:"Server:" %REPORT_FILE% >nul
if %errorlevel%==0 (
    echo   ✅ 发现服务器信息:
    findstr /C:"Server:" %REPORT_FILE%
) else (
    echo   ❌ 未发现服务器头部信息
)

findstr /C:"X-Powered-By:" %REPORT_FILE% >nul
if %errorlevel%==0 (
    echo   ✅ 发现技术栈信息:
    findstr /C:"X-Powered-By:" %REPORT_FILE%
) else (
    echo   ❌ 未发现技术栈信息
)

echo.
echo 🎯 服务商特征分析:

REM 检查常见的错误码模式
findstr /C:"\"code\"" %REPORT_FILE% >nul
if %errorlevel%==0 (
    echo   ✅ 发现"code"字段 - 可能是云片网络或创蓝253
)

findstr /C:"\"statusCode\"" %REPORT_FILE% >nul
if %errorlevel%==0 (
    echo   ✅ 发现"statusCode"字段 - 可能是容联云通讯
)

findstr /C:"\"status\"" %REPORT_FILE% >nul
if %errorlevel%==0 (
    echo   ✅ 发现"status"字段 - 可能是SUBMAIL
)

findstr /C:"\"result\"" %REPORT_FILE% >nul
if %errorlevel%==0 (
    echo   ✅ 发现"result"字段 - 可能是亿美软通
)

echo.
echo 💡 建议下一步:
echo   1. 查看详细报告文件: %REPORT_FILE%
echo   2. 申请测试账号进行实际API调用
echo   3. 分析成功响应的JSON结构
echo   4. 通过实际短信发送确定服务商身份

echo.
echo 📋 快速查看关键响应:
echo.
echo --- 登录页面内容预览 ---
if exist login_page.html (
    type login_page.html | findstr /C:"title" /C:"登录" /C:"短信" /C:"平台" /C:"公司"
)

echo.
echo --- HTTP状态码统计 ---
findstr /C:"HTTP状态码:" %REPORT_FILE% | sort

echo.
echo ============================================================
echo 分析完成！请查看 %REPORT_FILE% 获取完整详情
echo ============================================================

pause
