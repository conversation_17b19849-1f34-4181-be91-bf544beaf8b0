{"metrics": {"http_req_waiting": {"avg": 1341.9903896514168, "min": 0, "med": 1130.5598, "max": 5532.3167, "p(90)": 2070.05094, "p(95)": 2252.94138, "p(99)": 4163.378739000003}, "http_req_blocked": {"avg": 0.9995970588235297, "min": 0, "med": 1.0941, "max": 11.1751, "p(90)": 1.7093600000000002, "p(95)": 1.7852, "p(99)": 2.4365}, "custom_response_time": {"p(90)": 2079.2, "p(95)": 2264.2, "p(99)": 4313.920000000005, "avg": 1419.980370774264, "min": 1021, "med": 1145, "max": 21053, "thresholds": {"p(95)<400": true}}, "vus": {"value": 1, "min": 0, "max": 50}, "data_sent": {"count": 92383, "rate": 1103.27045306384}, "vus_max": {"value": 50, "min": 50, "max": 50}, "http_req_duration": {"med": 1130.7683, "max": 5532.3167, "p(90)": 2070.05094, "p(95)": 2252.950545, "p(99)": 4163.852918000004, "avg": 1342.0837681917214, "min": 0, "thresholds": {"p(95)<500": true, "p(99)<1000": true}}, "http_reqs": {"rate": 10.963080609122946, "count": 918}, "custom_error_rate": {"passes": 917, "fails": 0, "thresholds": {"rate<0.05": true}, "value": 1}, "http_req_connecting": {"p(90)": 1.6669900000000004, "p(95)": 1.7841, "p(99)": 2.4365, "avg": 0.9378666666666665, "min": 0, "med": 0.9991, "max": 5.7712}, "http_req_tls_handshaking": {"avg": 0, "min": 0, "med": 0, "max": 0, "p(90)": 0, "p(95)": 0, "p(99)": 0}, "http_req_sending": {"avg": 0.09337854030501093, "min": 0, "med": 0, "max": 1.4715, "p(90)": 0.51054, "p(95)": 0.55979, "p(99)": 1.0143010000000001}, "http_req_receiving": {"p(99)": 0, "avg": 0, "min": 0, "med": 0, "max": 0, "p(90)": 0, "p(95)": 0}, "active_users": {"value": -1, "min": -1, "max": 1}, "custom_request_count": {"count": 917, "rate": 10.951138255518238}, "iterations": {"count": 917, "rate": 10.951138255518238}, "checks": {"passes": 1711, "fails": 2874, "value": 0.3731733914940022}, "data_received": {"count": 0, "rate": 0}, "iteration_duration": {"avg": 3440.021837622683, "min": 2054.4328, "med": 3299.9525, "max": 23419.0688, "p(90)": 4243.38154, "p(95)": 4763.0057799999995, "p(99)": 6723.956308000001}, "http_req_failed": {"passes": 918, "fails": 0, "thresholds": {"rate<0.1": true}, "value": 1}}, "setup_data": {"method": "GET", "startTime": "2025-07-31T16:36:58.009+08:00", "targetUrl": "http://youyihuas.xyz/"}, "root_group": {"name": "", "path": "", "id": "d41d8cd98f00b204e9800998ecf8427e", "groups": {}, "checks": {"✅ HTTP状态码正常": {"name": "✅ HTTP状态码正常", "path": "::✅ HTTP状态码正常", "id": "9e3542fef63c938951dffb9a1540a3f3", "passes": 0, "fails": 917}, "⚡ 响应时间合理": {"name": "⚡ 响应时间合理", "path": "::⚡ 响应时间合理", "id": "462889065fdc366f299e485ae317651f", "passes": 794, "fails": 123}, "📦 响应体存在": {"id": "4e0344942734d025a6975abdb1ca4eff", "passes": 0, "fails": 917, "name": "📦 响应体存在", "path": "::📦 响应体存在"}, "🔗 连接建立成功": {"name": "🔗 连接建立成功", "path": "::🔗 连接建立成功", "id": "f73c0aceea33f4cb8fff4b2c25ebc861", "passes": 917, "fails": 0}, "📡 DNS解析成功": {"path": "::📡 DNS解析成功", "id": "18a0742ac0b3c22eeaee1530099431c5", "passes": 0, "fails": 917, "name": "📡 DNS解析成功"}}}}