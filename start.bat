@echo off
title mitmproxy Web Interface
echo ========================================
echo    mitmproxy Web Interface
echo ========================================
echo.
echo Web Interface: http://127.0.0.1:8081
echo Proxy Address: %COMPUTERNAME%:8080
echo.
echo Instructions:
echo 1. Set phone proxy to: %COMPUTERNAME%:8080
echo 2. Visit mitm.it on phone to install certificate
echo 3. Start capturing traffic!
echo.
cd /d "E:\mitmproxy-setup"
mitmweb --web-host 0.0.0.0 --web-port 8081 --listen-port 8080 --set confdir=E:\mitmproxy-setup
