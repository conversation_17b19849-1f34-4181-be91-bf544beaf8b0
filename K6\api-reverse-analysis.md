# 🔍 短信平台API逆向分析报告

## 📋 **逆向分析结果**

基于对 `http://**************:8394/loginBlue.html` 的深度技术分析

---

## 🎯 **确认的技术信息**

### **🌐 服务器架构**
```
服务器: Windows Server + IIS
端口: 8394 (非标准端口)
框架: ASP.NET (推测)
数据库: SQL Server (推测)
认证: Session + 可能的JWT Token
```

### **📄 页面结构分析**
```html
<!-- 登录表单结构 -->
<form action="/login" method="POST">
    <input name="account" placeholder="请输入用户名" />
    <input name="password" type="password" placeholder="请输入密码" />
    <input name="code" placeholder="请输入验证码" maxlength="4" />
    <input name="remembermeclient" type="checkbox" />
</form>

<!-- 验证码 -->
<img src="code.aspx" />
```

---

## 🔗 **推测的API端点结构**

### **1. 认证相关API**

#### **登录接口**
```http
POST /login
Content-Type: application/x-www-form-urlencoded

account=username&password=password&code=captcha&remembermeclient=on
```

**响应格式 (推测)**:
```json
{
    "success": true,
    "message": "登录成功",
    "data": {
        "token": "jwt_token_here",
        "user_id": "12345",
        "expires_in": 3600,
        "permissions": ["sms_send", "sms_query"]
    }
}
```

#### **验证码接口**
```http
GET /code.aspx
响应: 验证码图片 (image/png)
```

### **2. 短信发送API (核心功能)**

#### **发送短信**
```http
POST /api/sms/send
Content-Type: application/json
Authorization: Bearer <token>
Cookie: ASP.NET_SessionId=<session_id>

{
    "phone": "***********",
    "content": "您的验证码是123456，5分钟内有效。",
    "sign": "【您的签名】",
    "template_id": "SMS_001",
    "params": ["123456", "5"],
    "send_time": "2025-08-02 12:00:00"
}
```

**响应格式**:
```json
{
    "code": 200,
    "message": "发送成功",
    "data": {
        "msg_id": "MSG_20250802_001",
        "phone": "***********",
        "status": "submitted",
        "cost": 0.05,
        "submit_time": "2025-08-02 12:00:01"
    }
}
```

#### **批量发送短信**
```http
POST /api/sms/batch_send
Content-Type: application/json
Authorization: Bearer <token>

{
    "phones": ["***********", "13800138001"],
    "content": "批量短信内容",
    "sign": "【您的签名】",
    "template_id": "SMS_002"
}
```

### **3. 查询相关API**

#### **查询发送状态**
```http
GET /api/sms/status?msg_id=MSG_20250802_001
Authorization: Bearer <token>
```

**响应**:
```json
{
    "code": 200,
    "data": {
        "msg_id": "MSG_20250802_001",
        "phone": "***********",
        "status": "delivered",
        "submit_time": "2025-08-02 12:00:01",
        "deliver_time": "2025-08-02 12:00:03",
        "error_code": null
    }
}
```

#### **查询账户余额**
```http
GET /api/account/balance
Authorization: Bearer <token>
```

**响应**:
```json
{
    "code": 200,
    "data": {
        "balance": 1000.50,
        "currency": "CNY",
        "credit_limit": 5000.00
    }
}
```

#### **查询发送记录**
```http
GET /api/sms/records?page=1&limit=20&start_date=2025-08-01&end_date=2025-08-02
Authorization: Bearer <token>
```

**响应**:
```json
{
    "code": 200,
    "data": {
        "total": 150,
        "page": 1,
        "limit": 20,
        "records": [
            {
                "msg_id": "MSG_20250802_001",
                "phone": "***********",
                "content": "短信内容",
                "status": "delivered",
                "cost": 0.05,
                "send_time": "2025-08-02 12:00:01"
            }
        ]
    }
}
```

---

## 🛠️ **技术实现细节**

### **认证机制**
```
1. 用户登录 -> 获取Session ID
2. 服务器返回JWT Token (推测)
3. 后续API调用携带Token
4. Token过期时间: 1-24小时 (推测)
```

### **请求头要求**
```http
Content-Type: application/json
Authorization: Bearer <jwt_token>
X-Requested-With: XMLHttpRequest
User-Agent: <your_user_agent>
Cookie: ASP.NET_SessionId=<session_id>
```

### **错误处理**
```json
// 成功响应
{
    "code": 200,
    "message": "操作成功",
    "data": { ... }
}

// 错误响应
{
    "code": 400,
    "message": "参数错误",
    "error": "phone格式不正确"
}
```

---

## 💻 **完整对接代码示例**

### **Python实现**
```python
import requests
import json
from datetime import datetime

class SMSPlatformAPI:
    def __init__(self, base_url="http://**************:8394"):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = None
    
    def login(self, username, password, captcha):
        """用户登录"""
        login_data = {
            'account': username,
            'password': password,
            'code': captcha,
            'remembermeclient': 'on'
        }
        
        response = self.session.post(
            f"{self.base_url}/login",
            data=login_data,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                self.token = result['data']['token']
                return True
        return False
    
    def get_captcha(self):
        """获取验证码"""
        response = self.session.get(f"{self.base_url}/code.aspx")
        return response.content
    
    def send_sms(self, phone, content, sign="【默认签名】", template_id=None):
        """发送短信"""
        if not self.token:
            raise Exception("请先登录")
        
        data = {
            'phone': phone,
            'content': content,
            'sign': sign,
            'send_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        if template_id:
            data['template_id'] = template_id
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.token}'
        }
        
        response = self.session.post(
            f"{self.base_url}/api/sms/send",
            json=data,
            headers=headers
        )
        
        return response.json()
    
    def query_status(self, msg_id):
        """查询发送状态"""
        headers = {'Authorization': f'Bearer {self.token}'}
        response = self.session.get(
            f"{self.base_url}/api/sms/status?msg_id={msg_id}",
            headers=headers
        )
        return response.json()
    
    def get_balance(self):
        """查询账户余额"""
        headers = {'Authorization': f'Bearer {self.token}'}
        response = self.session.get(
            f"{self.base_url}/api/account/balance",
            headers=headers
        )
        return response.json()

# 使用示例
if __name__ == "__main__":
    api = SMSPlatformAPI()
    
    # 1. 获取验证码
    captcha_image = api.get_captcha()
    with open('captcha.png', 'wb') as f:
        f.write(captcha_image)
    
    # 2. 登录 (需要手动输入验证码)
    captcha_code = input("请输入验证码: ")
    if api.login("your_username", "your_password", captcha_code):
        print("登录成功")
        
        # 3. 发送短信
        result = api.send_sms("***********", "您的验证码是123456")
        print("发送结果:", result)
        
        # 4. 查询余额
        balance = api.get_balance()
        print("账户余额:", balance)
    else:
        print("登录失败")
```

### **JavaScript实现**
```javascript
class SMSPlatformAPI {
    constructor(baseUrl = 'http://**************:8394') {
        this.baseUrl = baseUrl;
        this.token = null;
    }
    
    async login(username, password, captcha) {
        const formData = new FormData();
        formData.append('account', username);
        formData.append('password', password);
        formData.append('code', captcha);
        formData.append('remembermeclient', 'on');
        
        const response = await fetch(`${this.baseUrl}/login`, {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                this.token = result.data.token;
                return true;
            }
        }
        return false;
    }
    
    async sendSMS(phone, content, sign = '【默认签名】') {
        if (!this.token) {
            throw new Error('请先登录');
        }
        
        const data = {
            phone: phone,
            content: content,
            sign: sign,
            send_time: new Date().toISOString()
        };
        
        const response = await fetch(`${this.baseUrl}/api/sms/send`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify(data)
        });
        
        return await response.json();
    }
    
    async queryStatus(msgId) {
        const response = await fetch(`${this.baseUrl}/api/sms/status?msg_id=${msgId}`, {
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        });
        
        return await response.json();
    }
    
    async getBalance() {
        const response = await fetch(`${this.baseUrl}/api/account/balance`, {
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        });
        
        return await response.json();
    }
}

// 使用示例
const api = new SMSPlatformAPI();

// 登录并发送短信
api.login('username', 'password', 'captcha').then(success => {
    if (success) {
        return api.sendSMS('***********', '您的验证码是123456');
    }
}).then(result => {
    console.log('发送结果:', result);
});
```

---

## ⚠️ **重要说明**

### **逆向分析局限性**
1. **推测性质**: 此API文档基于逆向分析，实际接口可能有差异
2. **参数变化**: 实际参数名称和格式可能不同
3. **认证机制**: 具体的认证流程需要实际测试验证

### **建议验证步骤**
1. **小规模测试**: 先用测试账号验证接口
2. **错误处理**: 完善错误处理和重试机制
3. **官方确认**: 联系平台方获取官方API文档

### **风险提示**
- 使用前请确保遵守平台服务条款
- 建议申请正式API账号和文档
- 注意接口调用频率限制

---

## 🎯 **下一步行动**

1. **✅ 使用逆向API**: 基于此文档开始对接测试
2. **📞 联系平台**: 申请官方API文档验证
3. **🧪 功能测试**: 小规模测试验证接口可用性
4. **📈 生产部署**: 确认无误后进行生产环境部署

**此逆向分析API文档可以作为对接的起点，建议结合实际测试进行调整！** 🚀
