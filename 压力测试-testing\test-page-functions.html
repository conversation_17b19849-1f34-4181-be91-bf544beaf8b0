<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K6 Interface Function Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-section { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input, select { padding: 5px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🧪 K6 Interface Function Test</h1>
    
    <div class="test-section">
        <h2>📋 Test Form Elements</h2>
        <div>
            <label>Quick Scenario:</label>
            <select id="quickScenario">
                <option value="">Select Scenario</option>
                <option value="beginner">Beginner</option>
                <option value="stress-test">Stress Test</option>
            </select>
        </div>
        <div>
            <label>Virtual Users:</label>
            <input type="number" id="virtualUsers" value="10" min="1" max="1000">
        </div>
        <div>
            <label>Duration:</label>
            <select id="duration">
                <option value="30s">30 seconds</option>
                <option value="1m">1 minute</option>
                <option value="5m">5 minutes</option>
            </select>
        </div>
        <div>
            <label>Test URL:</label>
            <input type="url" id="testUrl" value="https://httpbin.org/get">
        </div>
        <div>
            <label><input type="checkbox" id="enableProxy"> Enable Proxy</label>
        </div>
        <div>
            <label>Proxy Host:</label>
            <input type="text" id="proxyHost" value="127.0.0.1">
        </div>
        <div>
            <label>Proxy Port:</label>
            <input type="number" id="proxyPort" value="1080">
        </div>
        <div>
            <label><input type="checkbox" id="proxyAuth"> Proxy Auth</label>
        </div>
        <div>
            <label>Username:</label>
            <input type="text" id="proxyUsername" value="testuser">
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="proxyPassword" value="testpass">
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Function Tests</h2>
        <button onclick="testScenarioFunction()">Test Scenario Function</button>
        <button onclick="testElementAccess()">Test Element Access</button>
        <button onclick="testDataRetrieval()">Test Data Retrieval</button>
        <button onclick="testCommandGeneration()">Test Command Generation</button>
        <div id="testResults"></div>
    </div>

    <script>
        // Test scenario function
        function testScenarioFunction() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h3>Testing Scenario Function...</h3>';
            
            try {
                // Test scenario selection
                document.getElementById('quickScenario').value = 'stress-test';
                document.getElementById('virtualUsers').value = '20';
                
                // Simulate scenario application
                const scenario = document.getElementById('quickScenario').value;
                const currentVus = parseInt(document.getElementById('virtualUsers').value) || 5;
                
                const scenarios = {
                    'beginner': {
                        vus: Math.max(1, Math.floor(currentVus * 0.4)),
                        duration: '30s',
                        description: 'Beginner test'
                    },
                    'stress-test': {
                        vus: Math.floor(currentVus * 2),
                        duration: '5m',
                        description: 'Stress test'
                    }
                };
                
                const config = scenarios[scenario];
                if (config) {
                    document.getElementById('virtualUsers').value = config.vus;
                    document.getElementById('duration').value = config.duration;
                    
                    results.innerHTML += `<div class="test-result pass">✅ Scenario function works: ${config.description} - ${config.vus} users, ${config.duration}</div>`;
                } else {
                    results.innerHTML += `<div class="test-result fail">❌ Scenario function failed: No config found</div>`;
                }
            } catch (error) {
                results.innerHTML += `<div class="test-result fail">❌ Scenario function error: ${error.message}</div>`;
            }
        }
        
        // Test element access
        function testElementAccess() {
            const results = document.getElementById('testResults');
            results.innerHTML += '<h3>Testing Element Access...</h3>';
            
            const elements = [
                'quickScenario', 'virtualUsers', 'duration', 'testUrl',
                'enableProxy', 'proxyHost', 'proxyPort', 'proxyAuth',
                'proxyUsername', 'proxyPassword'
            ];
            
            let passCount = 0;
            let failCount = 0;
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    results.innerHTML += `<div class="test-result pass">✅ Element found: ${id}</div>`;
                    passCount++;
                } else {
                    results.innerHTML += `<div class="test-result fail">❌ Element missing: ${id}</div>`;
                    failCount++;
                }
            });
            
            results.innerHTML += `<div class="test-result ${failCount === 0 ? 'pass' : 'fail'}">📊 Element Access Summary: ${passCount} passed, ${failCount} failed</div>`;
        }
        
        // Test data retrieval
        function testDataRetrieval() {
            const results = document.getElementById('testResults');
            results.innerHTML += '<h3>Testing Data Retrieval...</h3>';
            
            try {
                const url = document.getElementById('testUrl').value;
                const vus = document.getElementById('virtualUsers').value;
                const duration = document.getElementById('duration').value;
                const proxyEnabled = document.getElementById('enableProxy').checked;
                const proxyHost = document.getElementById('proxyHost').value;
                const proxyPort = document.getElementById('proxyPort').value;
                
                results.innerHTML += `<div class="test-result pass">✅ URL: ${url}</div>`;
                results.innerHTML += `<div class="test-result pass">✅ VUs: ${vus}</div>`;
                results.innerHTML += `<div class="test-result pass">✅ Duration: ${duration}</div>`;
                results.innerHTML += `<div class="test-result pass">✅ Proxy Enabled: ${proxyEnabled}</div>`;
                results.innerHTML += `<div class="test-result pass">✅ Proxy Host: ${proxyHost}</div>`;
                results.innerHTML += `<div class="test-result pass">✅ Proxy Port: ${proxyPort}</div>`;
                
            } catch (error) {
                results.innerHTML += `<div class="test-result fail">❌ Data retrieval error: ${error.message}</div>`;
            }
        }
        
        // Test command generation
        function testCommandGeneration() {
            const results = document.getElementById('testResults');
            results.innerHTML += '<h3>Testing Command Generation...</h3>';
            
            try {
                const url = document.getElementById('testUrl').value;
                const vus = document.getElementById('virtualUsers').value;
                const duration = document.getElementById('duration').value;
                const selectedScenario = document.getElementById('quickScenario').value;
                
                let k6Command = 'k6 run examples/proxy-test.js --out json=summary.json --summary-export=summary.json';
                const envVars = [];
                
                envVars.push('-e TARGET_URL=' + url);
                envVars.push('-e VUS=' + vus);
                envVars.push('-e DURATION=' + duration);
                
                if (selectedScenario) {
                    if (selectedScenario === 'stress-test') {
                        envVars.push('-e SCENARIO=gradual');
                        envVars.push('-e MAX_VUS=' + vus);
                    } else {
                        envVars.push('-e SCENARIO=simple');
                    }
                }
                
                k6Command += ' ' + envVars.join(' ');
                
                results.innerHTML += `<div class="test-result pass">✅ Command generated successfully</div>`;
                results.innerHTML += `<div class="test-result pass">📋 Command: ${k6Command}</div>`;
                
            } catch (error) {
                results.innerHTML += `<div class="test-result fail">❌ Command generation error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
