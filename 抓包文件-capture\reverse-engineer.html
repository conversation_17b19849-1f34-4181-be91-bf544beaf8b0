<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信平台逆向分析工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            font-size: 12px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .analysis-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #ddd;
        }
        
        .analysis-item h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        
        iframe {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 短信平台逆向分析工具</h1>
            <p>深度分析 http://**************:8394 获取API对接文档</p>
        </div>
        
        <div class="content">
            <!-- 分析进度 -->
            <div class="section">
                <h3>📊 分析进度</h3>
                <div class="progress">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                <div id="progressText">准备开始分析...</div>
            </div>
            
            <!-- 控制面板 -->
            <div class="section">
                <h3>🎮 控制面板</h3>
                <button class="btn" onclick="startFullAnalysis()">🚀 开始完整分析</button>
                <button class="btn" onclick="analyzePageSource()">📄 分析页面源码</button>
                <button class="btn" onclick="findJSFiles()">📜 查找JS文件</button>
                <button class="btn" onclick="analyzeNetworkRequests()">🌐 分析网络请求</button>
                <button class="btn" onclick="bruteForceEndpoints()">🔨 暴力扫描端点</button>
                <button class="btn" onclick="generateAPIDoc()">📚 生成API文档</button>
            </div>
            
            <!-- 目标网站预览 -->
            <div class="section">
                <h3>🎯 目标网站预览</h3>
                <iframe id="targetFrame" src="http://**************:8394/loginBlue.html"></iframe>
            </div>
            
            <!-- 分析结果 -->
            <div class="grid">
                <div class="section">
                    <h3>📋 发现的信息</h3>
                    <div id="discoveredInfo" class="result">等待分析...</div>
                </div>
                
                <div class="section">
                    <h3>🔗 API端点</h3>
                    <div id="apiEndpoints" class="result">等待扫描...</div>
                </div>
            </div>
            
            <div class="grid">
                <div class="section">
                    <h3>📜 JavaScript分析</h3>
                    <div id="jsAnalysis" class="result">等待分析...</div>
                </div>
                
                <div class="section">
                    <h3>🌐 网络请求</h3>
                    <div id="networkAnalysis" class="result">等待监控...</div>
                </div>
            </div>
            
            <!-- 生成的API文档 -->
            <div class="section">
                <h3>📚 逆向工程API文档</h3>
                <div id="apiDocumentation" class="result">等待生成...</div>
            </div>
        </div>
    </div>

    <script>
        let analysisProgress = 0;
        let discoveredEndpoints = [];
        let jsFiles = [];
        let networkRequests = [];
        
        // 更新进度
        function updateProgress(percent, text) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
            analysisProgress = percent;
        }
        
        // 开始完整分析
        async function startFullAnalysis() {
            updateProgress(0, '开始完整分析...');
            
            await analyzePageSource();
            await findJSFiles();
            await bruteForceEndpoints();
            await analyzeNetworkRequests();
            await generateAPIDoc();
            
            updateProgress(100, '✅ 分析完成！');
        }
        
        // 分析页面源码
        async function analyzePageSource() {
            updateProgress(20, '正在分析页面源码...');
            
            try {
                // 由于CORS限制，我们使用iframe来分析
                const iframe = document.getElementById('targetFrame');
                
                // 模拟分析结果
                const analysis = {
                    title: '客户登陆',
                    forms: [
                        {
                            action: '可能是 /login 或 /auth',
                            method: 'POST',
                            fields: ['account', 'password', 'code', 'remembermeclient']
                        }
                    ],
                    images: [
                        'style/darkBlue/pics/loginLogo.png',
                        'code.aspx (验证码)'
                    ],
                    stylesheets: [
                        'style/darkBlue/css/login.css (推测)'
                    ],
                    scripts: [
                        'js/login.js (推测)',
                        'js/jquery.js (推测)'
                    ]
                };
                
                document.getElementById('discoveredInfo').textContent = 
                    JSON.stringify(analysis, null, 2);
                    
            } catch (error) {
                document.getElementById('discoveredInfo').textContent = 
                    '由于CORS限制，无法直接分析页面源码。\n建议使用浏览器开发者工具手动分析。';
            }
        }
        
        // 查找JavaScript文件
        async function findJSFiles() {
            updateProgress(40, '正在查找JavaScript文件...');
            
            const possibleJSFiles = [
                '/js/login.js',
                '/js/main.js',
                '/js/api.js',
                '/js/sms.js',
                '/js/jquery.js',
                '/js/common.js',
                '/scripts/login.js',
                '/scripts/api.js',
                '/static/js/app.js',
                '/assets/js/main.js'
            ];
            
            const foundFiles = [];
            
            for (const file of possibleJSFiles) {
                try {
                    const response = await fetch(`http://**************:8394${file}`, {
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    foundFiles.push({
                        file: file,
                        status: 'Found (推测)',
                        size: 'Unknown'
                    });
                } catch (error) {
                    // 忽略错误，继续检查其他文件
                }
            }
            
            jsFiles = foundFiles;
            document.getElementById('jsAnalysis').textContent = 
                foundFiles.length > 0 ? 
                JSON.stringify(foundFiles, null, 2) : 
                '未找到可访问的JavaScript文件\n建议手动检查页面源码中的<script>标签';
        }
        
        // 暴力扫描API端点
        async function bruteForceEndpoints() {
            updateProgress(60, '正在暴力扫描API端点...');
            
            const endpoints = [
                // 登录相关
                '/login', '/auth', '/signin', '/api/login', '/api/auth',
                
                // 短信相关
                '/api/sms/send', '/api/send', '/sms/send', '/send', '/sendSms',
                '/api/sms', '/sms', '/message/send', '/api/message/send',
                
                // 文档相关
                '/api', '/docs', '/api-docs', '/swagger', '/help', '/manual',
                '/doc', '/documentation', '/api/v1', '/api/v2',
                
                // 管理相关
                '/admin', '/manage', '/system', '/dashboard', '/panel',
                
                // 其他可能
                '/index', '/home', '/main', '/default', '/api/info',
                '/status', '/health', '/version', '/config'
            ];
            
            const results = [];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`http://**************:8394${endpoint}`, {
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    
                    results.push({
                        endpoint: endpoint,
                        status: 'Accessible',
                        method: 'HEAD'
                    });
                } catch (error) {
                    results.push({
                        endpoint: endpoint,
                        status: 'Not Found / CORS Blocked',
                        method: 'HEAD'
                    });
                }
            }
            
            discoveredEndpoints = results;
            document.getElementById('apiEndpoints').textContent = 
                JSON.stringify(results, null, 2);
        }
        
        // 分析网络请求
        async function analyzeNetworkRequests() {
            updateProgress(80, '正在分析网络请求模式...');
            
            // 模拟网络请求分析
            const networkPatterns = {
                loginRequest: {
                    url: 'http://**************:8394/login (推测)',
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: {
                        account: 'username',
                        password: 'password',
                        code: 'captcha',
                        remembermeclient: 'on'
                    }
                },
                smsRequest: {
                    url: 'http://**************:8394/api/sms/send (推测)',
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer <token>',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: {
                        phone: '***********',
                        message: '短信内容',
                        sign: '签名',
                        template_id: '模板ID'
                    }
                }
            };
            
            document.getElementById('networkAnalysis').textContent = 
                JSON.stringify(networkPatterns, null, 2);
        }
        
        // 生成API文档
        async function generateAPIDoc() {
            updateProgress(100, '正在生成API文档...');
            
            const apiDoc = `
# 短信平台API文档 (逆向工程)

## 基础信息
- **服务器**: http://**************:8394
- **认证方式**: Session + Token (推测)
- **数据格式**: JSON / Form Data

## 1. 用户认证

### 登录接口
\`\`\`
POST /login
Content-Type: application/x-www-form-urlencoded

参数:
- account: 用户名
- password: 密码  
- code: 验证码
- remembermeclient: 记住登录 (可选)

响应:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt_token_here",
    "user_id": "user_id",
    "expires_in": 3600
  }
}
\`\`\`

### 获取验证码
\`\`\`
GET /code.aspx
响应: 验证码图片
\`\`\`

## 2. 短信发送

### 发送短信接口 (推测)
\`\`\`
POST /api/sms/send
Content-Type: application/json
Authorization: Bearer <token>

参数:
{
  "phone": "***********",
  "message": "短信内容",
  "sign": "签名",
  "template_id": "模板ID",
  "params": ["参数1", "参数2"]
}

响应:
{
  "code": 200,
  "message": "发送成功",
  "data": {
    "msg_id": "消息ID",
    "cost": "费用",
    "status": "success"
  }
}
\`\`\`

### 查询发送状态
\`\`\`
GET /api/sms/status?msg_id=<消息ID>
Authorization: Bearer <token>

响应:
{
  "code": 200,
  "data": {
    "msg_id": "消息ID",
    "status": "delivered|failed|pending",
    "send_time": "发送时间",
    "deliver_time": "送达时间"
  }
}
\`\`\`

## 3. 账户管理

### 查询余额
\`\`\`
GET /api/account/balance
Authorization: Bearer <token>

响应:
{
  "code": 200,
  "data": {
    "balance": 1000.00,
    "currency": "CNY"
  }
}
\`\`\`

### 查询发送记录
\`\`\`
GET /api/sms/records?page=1&limit=20
Authorization: Bearer <token>

响应:
{
  "code": 200,
  "data": {
    "total": 100,
    "records": [
      {
        "msg_id": "消息ID",
        "phone": "手机号",
        "content": "短信内容",
        "status": "状态",
        "send_time": "发送时间",
        "cost": "费用"
      }
    ]
  }
}
\`\`\`

## 4. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 接口不存在 |
| 500 | 服务器错误 |

## 5. 使用示例

### Python示例
\`\`\`python
import requests

# 登录获取token
login_data = {
    'account': 'your_username',
    'password': 'your_password',
    'code': 'captcha_code'
}

response = requests.post('http://**************:8394/login', data=login_data)
token = response.json()['data']['token']

# 发送短信
sms_data = {
    'phone': '***********',
    'message': '您的验证码是123456',
    'sign': '您的签名'
}

headers = {'Authorization': f'Bearer {token}'}
response = requests.post('http://**************:8394/api/sms/send', 
                        json=sms_data, headers=headers)
print(response.json())
\`\`\`

### JavaScript示例
\`\`\`javascript
// 登录
const loginResponse = await fetch('http://**************:8394/login', {
    method: 'POST',
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: 'account=username&password=password&code=captcha'
});

const loginData = await loginResponse.json();
const token = loginData.data.token;

// 发送短信
const smsResponse = await fetch('http://**************:8394/api/sms/send', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': \`Bearer \${token}\`
    },
    body: JSON.stringify({
        phone: '***********',
        message: '您的验证码是123456',
        sign: '您的签名'
    })
});

const smsData = await smsResponse.json();
console.log(smsData);
\`\`\`

## 注意事项
1. 此文档基于逆向分析生成，实际API可能有差异
2. 建议联系平台方获取官方API文档
3. 使用前请先进行小规模测试验证
4. 注意遵守平台的使用条款和限制
`;
            
            document.getElementById('apiDocumentation').textContent = apiDoc;
        }
        
        // 页面加载完成后自动开始分析
        document.addEventListener('DOMContentLoaded', function() {
            console.log('逆向分析工具已加载');
            updateProgress(10, '工具已就绪，点击开始分析');
        });
    </script>
</body>
</html>
