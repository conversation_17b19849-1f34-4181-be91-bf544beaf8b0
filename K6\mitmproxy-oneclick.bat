@echo off
title mitmproxy One-Click Setup

echo ========================================
echo    mitmproxy Web Interface Setup
echo ========================================
echo.

echo [1/5] Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    echo Please install Python from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)
echo OK: Python found

echo.
echo [2/5] Installing mitmproxy...
pip install mitmproxy
if %errorlevel% neq 0 (
    echo ERROR: Failed to install mitmproxy
    pause
    exit /b 1
)
echo OK: mitmproxy installed

echo.
echo [3/5] Creating directories...
if not exist "E:\mitmproxy-setup" mkdir "E:\mitmproxy-setup"
if not exist "E:\mitmproxy-setup\scripts" mkdir "E:\mitmproxy-setup\scripts"
if not exist "E:\mitmproxy-setup\certs" mkdir "E:\mitmproxy-setup\certs"
if not exist "E:\mitmproxy-setup\logs" mkdir "E:\mitmproxy-setup\logs"
echo OK: Directories created

echo.
echo [4/5] Creating startup script...
(
echo @echo off
echo title mitmproxy Web Interface
echo echo ========================================
echo echo    mitmproxy Web Interface
echo echo ========================================
echo echo.
echo echo Web Interface: http://127.0.0.1:8081
echo echo Proxy Address: %%COMPUTERNAME%%:8080
echo echo.
echo echo Instructions:
echo echo 1. Set phone proxy to: %%COMPUTERNAME%%:8080
echo echo 2. Visit mitm.it on phone to install certificate
echo echo 3. Start capturing traffic!
echo echo.
echo cd /d "E:\mitmproxy-setup"
echo mitmweb --web-host 0.0.0.0 --web-port 8081 --listen-port 8080 --set confdir=E:\mitmproxy-setup
) > "E:\mitmproxy-setup\start.bat"
echo OK: Startup script created

echo.
echo [5/5] Setup complete!
echo.
echo ========================================
echo SUCCESS: mitmproxy Web Interface Ready!
echo ========================================
echo.
echo Installation Directory: E:\mitmproxy-setup
echo Startup Script: E:\mitmproxy-setup\start.bat
echo Web Interface: http://127.0.0.1:8081
echo Proxy Address: %COMPUTERNAME%:8080
echo.
echo Next Steps:
echo 1. Run E:\mitmproxy-setup\start.bat
echo 2. Configure phone proxy settings
echo 3. Install certificate from mitm.it
echo 4. Start capturing!
echo.

set /p launch=Launch mitmproxy now? (y/n): 
if /i "%launch%"=="y" (
    echo.
    echo Launching mitmproxy Web Interface...
    start "" "E:\mitmproxy-setup\start.bat"
    timeout /t 3 >nul
    start "" "http://127.0.0.1:8081"
)

echo.
echo Setup completed successfully!
pause
