@echo off
title K6 Performance Testing with Proxy Detection

echo ========================================
echo K6 Performance Testing with Proxy Check
echo ========================================
echo.

REM Basic configuration
set API_URL=http://youyihuas.xyz/
set PROXY_HOST=isp.decodo.com
set PROXY_PORT=10001
set PROXY_USER=spo5nyyeb6
set PROXY_PASS=j6IJjtrro64Fie=l2W
set K6_PATH=C:\ProgramData\chocolatey\bin\k6.exe

REM ========================================
REM API Request Configuration
REM ========================================
REM Set the request method, e.g., GET, POST, PUT, DELETE
set REQUEST_METHOD=GET
REM Set the request body for POST/PUT requests. This is ignored for GET requests.
set REQUEST_DATA=
REM ========================================

echo [INFO] Configuration:
echo Target API: %API_URL%
echo Request Method: %REQUEST_METHOD%
echo Proxy Server: %PROXY_HOST%:%PROXY_PORT%
echo Proxy User: %PROXY_USER%
echo ========================================
echo.

echo [STEP 1] Testing Proxy Connection...
echo ========================================

REM Create a simple proxy test script
echo import http from 'k6/http'; > proxy_test_temp.js
echo import { check } from 'k6'; >> proxy_test_temp.js
echo. >> proxy_test_temp.js
echo export let options = { >> proxy_test_temp.js
echo   vus: 1, >> proxy_test_temp.js
echo   duration: '10s', >> proxy_test_temp.js
echo }; >> proxy_test_temp.js
echo. >> proxy_test_temp.js
echo export default function() { >> proxy_test_temp.js
echo   const proxyUrl = 'socks5://%PROXY_USER%:%PROXY_PASS%@%PROXY_HOST%:%PROXY_PORT%'; >> proxy_test_temp.js
echo   console.log('Testing proxy connection...'); >> proxy_test_temp.js
echo. >> proxy_test_temp.js
echo   const params = { >> proxy_test_temp.js
echo     proxy: proxyUrl, >> proxy_test_temp.js
echo     timeout: '30s' >> proxy_test_temp.js
echo   }; >> proxy_test_temp.js
echo. >> proxy_test_temp.js
echo   console.log('Testing basic connectivity...'); >> proxy_test_temp.js
echo   const response1 = http.get('https://httpbin.org/ip', params); >> proxy_test_temp.js
echo   const proxyWorking = check(response1, { >> proxy_test_temp.js
echo     'Proxy connection successful': (r) =^> r.status === 200, >> proxy_test_temp.js
echo     'Response time reasonable': (r) =^> r.timings.duration ^< 10000, >> proxy_test_temp.js
echo   }); >> proxy_test_temp.js
echo. >> proxy_test_temp.js
echo   if (proxyWorking) { >> proxy_test_temp.js
echo     console.log('SUCCESS: Proxy is working!'); >> proxy_test_temp.js
echo     console.log('Response time: ' + response1.timings.duration + 'ms'); >> proxy_test_temp.js
echo   } else { >> proxy_test_temp.js
echo     console.log('FAILED: Proxy connection failed!'); >> proxy_test_temp.js
echo     console.log('Status: ' + response1.status); >> proxy_test_temp.js
echo   } >> proxy_test_temp.js
echo } >> proxy_test_temp.js

echo [INFO] Running proxy connectivity test...
echo.

"%K6_PATH%" run proxy_test_temp.js

echo.
echo ========================================
echo [STEP 2] Proxy Test Results Analysis
echo ========================================

set /p continue=Do you want to continue with full performance test? (y/n): 

if /i "%continue%" neq "y" (
    echo.
    echo [INFO] Test cancelled by user
    del proxy_test_temp.js 2>nul
    pause
    exit /b
)

:main_menu
echo.
echo [STEP 3] Select Performance Test Scenario
echo ========================================
echo.
echo 1. Simple Load Test (customize parameters)
echo 2. Gradual Load Test (customize ramp-up parameters)
echo 3. Spike Test (customize spike parameters)
echo 4. Endurance Test (customize endurance parameters)
echo 5. Light Test (customize light test parameters)
echo 6. Extreme Test (customize extreme test parameters)
echo 7. Custom Test (enter VUs and duration)
echo 0. Exit
echo.

set /p choice=Enter your choice (0-7):

if "%choice%"=="0" goto :end
if "%choice%"=="1" goto :simple
if "%choice%"=="2" goto :gradual
if "%choice%"=="3" goto :spike
if "%choice%"=="4" goto :endurance
if "%choice%"=="5" goto :light
if "%choice%"=="6" goto :extreme
if "%choice%"=="7" goto :custom

echo Invalid choice. Please try again.
pause
goto :main_menu

:simple
echo.
echo [SIMPLE LOAD TEST] Configure Parameters
echo ========================================
set /p simple_vus=Enter number of virtual users (default 50):
set /p simple_duration=Enter test duration (e.g., 60s, 5m) (default 60s):

if "%simple_vus%"=="" set simple_vus=50
if "%simple_duration%"=="" set simple_duration=60s

echo.
echo [RUNNING] Simple Load Test with %simple_vus% users for %simple_duration%...
"%K6_PATH%" run examples\proxy-test.js ^
  --out json=simple_test.json ^
  --summary-export=simple_summary.json ^
  -e TARGET_URL=%API_URL% ^
  -e VUS=%simple_vus% ^
  -e DURATION=%simple_duration% ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USER=%PROXY_USER% ^
  -e PROXY_PASS=%PROXY_PASS%

echo.
echo [INFO] Simple Load Test completed!
echo Results saved to: simple_test.json and simple_summary.json
echo.
pause
goto :main_menu

:gradual
echo.
echo [GRADUAL LOAD TEST] Configure Parameters
echo ========================================
set /p gradual_max_vus=Enter maximum virtual users (default 50):
set /p gradual_duration=Enter total test duration (e.g., 8m, 10m) (default 8m):

if "%gradual_max_vus%"=="" set gradual_max_vus=50
if "%gradual_duration%"=="" set gradual_duration=8m

echo.
echo [RUNNING] Gradual Load Test ramping up to %gradual_max_vus% users over %gradual_duration%...
"%K6_PATH%" run examples\proxy-test.js ^
  --out json=gradual_test.json ^
  --summary-export=gradual_summary.json ^
  -e TARGET_URL=%API_URL% ^
  -e SCENARIO=gradual ^
  -e MAX_VUS=%gradual_max_vus% ^
  -e DURATION=%gradual_duration% ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USER=%PROXY_USER% ^
  -e PROXY_PASS=%PROXY_PASS%

echo.
echo [INFO] Gradual Load Test completed!
echo Results saved to: gradual_test.json and gradual_summary.json
echo.
pause
goto :main_menu

:spike
echo.
echo [SPIKE TEST] Configure Parameters
echo ========================================
set /p spike_vus=Enter spike virtual users (default 100):
set /p spike_duration=Enter test duration (e.g., 5m, 10m) (default 5m):

if "%spike_vus%"=="" set spike_vus=100
if "%spike_duration%"=="" set spike_duration=5m

echo.
echo [RUNNING] Spike Test with %spike_vus% users for %spike_duration%...
"%K6_PATH%" run examples\proxy-test.js ^
  --out json=spike_test.json ^
  --summary-export=spike_summary.json ^
  -e TARGET_URL=%API_URL% ^
  -e SCENARIO=spike ^
  -e SPIKE_VUS=%spike_vus% ^
  -e DURATION=%spike_duration% ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USER=%PROXY_USER% ^
  -e PROXY_PASS=%PROXY_PASS%

echo.
echo [INFO] Spike Test completed!
echo Results saved to: spike_test.json and spike_summary.json
echo.
pause
goto :main_menu

:endurance
echo.
echo [ENDURANCE TEST] Configure Parameters
echo ========================================
set /p endurance_vus=Enter virtual users (default 20):
set /p endurance_duration=Enter test duration (e.g., 10m, 30m) (default 10m):

if "%endurance_vus%"=="" set endurance_vus=20
if "%endurance_duration%"=="" set endurance_duration=10m

echo.
echo [RUNNING] Endurance Test with %endurance_vus% users for %endurance_duration%...
"%K6_PATH%" run examples\proxy-test.js ^
  --out json=endurance_test.json ^
  --summary-export=endurance_summary.json ^
  -e TARGET_URL=%API_URL% ^
  -e VUS=%endurance_vus% ^
  -e DURATION=%endurance_duration% ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USER=%PROXY_USER% ^
  -e PROXY_PASS=%PROXY_PASS%

echo.
echo [INFO] Endurance Test completed!
echo Results saved to: endurance_test.json and endurance_summary.json
echo.
pause
goto :main_menu

:light
echo.
echo [LIGHT TEST] Configure Parameters
echo ========================================
set /p light_vus=Enter virtual users (default 5):
set /p light_duration=Enter test duration (e.g., 30s, 1m) (default 30s):

if "%light_vus%"=="" set light_vus=5
if "%light_duration%"=="" set light_duration=30s

echo.
echo [RUNNING] Light Test with %light_vus% users for %light_duration%...
"%K6_PATH%" run examples\proxy-test.js ^
  --out json=light_test.json ^
  --summary-export=light_summary.json ^
  -e TARGET_URL=%API_URL% ^
  -e VUS=%light_vus% ^
  -e DURATION=%light_duration% ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USER=%PROXY_USER% ^
  -e PROXY_PASS=%PROXY_PASS%

echo.
echo [INFO] Light Test completed!
echo Results saved to: light_test.json and light_summary.json
echo.
pause
goto :main_menu

:extreme
echo.
echo [EXTREME TEST] Configure Parameters
echo ========================================
set /p extreme_vus=Enter virtual users (default 100):
set /p extreme_duration=Enter test duration (e.g., 3m, 5m) (default 3m):

if "%extreme_vus%"=="" set extreme_vus=100
if "%extreme_duration%"=="" set extreme_duration=3m

echo.
echo [RUNNING] Extreme Test with %extreme_vus% users for %extreme_duration%...
"%K6_PATH%" run examples\proxy-test.js ^
  --out json=extreme_test.json ^
  --summary-export=extreme_summary.json ^
  -e TARGET_URL=%API_URL% ^
  -e VUS=%extreme_vus% ^
  -e DURATION=%extreme_duration% ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USER=%PROXY_USER% ^
  -e PROXY_PASS=%PROXY_PASS%

echo.
echo [INFO] Extreme Test completed!
echo Results saved to: extreme_test.json and extreme_summary.json
echo.
pause
goto :main_menu

:custom
echo.
echo [CUSTOM TEST] Enter custom parameters:
set /p CUSTOM_VUS=Enter number of Virtual Users (VUs):
set /p CUSTOM_DURATION=Enter test duration (e.g., 30s, 5m, 1h):
echo.
echo [RUNNING] Custom Test with %CUSTOM_VUS% VUs for %CUSTOM_DURATION%...
"%K6_PATH%" run examples\proxy-test.js ^
  --out json=custom_test.json ^
  --summary-export=custom_summary.json ^
  -e TARGET_URL=%API_URL% ^
  -e VUS=%CUSTOM_VUS% ^
  -e DURATION=%CUSTOM_DURATION% ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USER=%PROXY_USER% ^
  -e PROXY_PASS=%PROXY_PASS%

echo.
echo [INFO] Custom Test completed!
echo Results saved to: custom_test.json and custom_summary.json
echo.
pause
goto :main_menu

:end
echo.
echo [INFO] Cleaning up temporary files...
del proxy_test_temp.js 2>nul
echo.
echo [INFO] Test completed! Check the generated JSON files for detailed results.
echo [INFO] Run create-report.bat to generate HTML reports.
echo.
pause
