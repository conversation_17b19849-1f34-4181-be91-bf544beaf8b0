@echo off
chcp 65001 >nul
title 查找API文档

echo ========================================
echo    查找短信平台API文档
echo ========================================
echo.

set BASE_URL=http://175.27.145.189:8394

echo 🎯 目标服务器: %BASE_URL%
echo.

echo 🔍 测试常见API文档路径...
echo.

REM 测试API文档路径
echo [1/15] 测试 /api
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/api" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [2/15] 测试 /docs
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/docs" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [3/15] 测试 /api-docs
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/api-docs" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [4/15] 测试 /swagger
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/swagger" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [5/15] 测试 /help
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/help" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [6/15] 测试 /manual
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/manual" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [7/15] 测试 /doc
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/doc" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [8/15] 测试 /documentation
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/documentation" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [9/15] 测试 /admin
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/admin" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [10/15] 测试 /manage
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/manage" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [11/15] 测试 /system
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/system" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [12/15] 测试 /api/sms/send
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/api/sms/send" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [13/15] 测试 /sms/send
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/sms/send" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [14/15] 测试 /send
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/send" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo [15/15] 测试 /sendSms
curl -s -o nul -w "Status: %%{http_code} - Size: %%{size_download} bytes" "%BASE_URL%/sendSms" 2>nul
if %errorlevel% equ 0 (
    echo  ✅
) else (
    echo  ❌
)

echo.
echo ========================================
echo    详细分析
echo ========================================
echo.

echo 🔍 获取主页详细信息...
curl -s "%BASE_URL%/loginBlue.html" > page_content.html
echo 页面内容已保存到 page_content.html

echo.
echo 🔍 查找页面中的关键信息...
findstr /i "api sms send ajax fetch" page_content.html > keywords.txt 2>nul
if exist keywords.txt (
    echo 找到相关关键词:
    type keywords.txt
) else (
    echo 未找到明显的API相关关键词
)

echo.
echo 🔍 查找JavaScript文件...
findstr /i "\.js" page_content.html > js_files.txt 2>nul
if exist js_files.txt (
    echo 找到JavaScript文件:
    type js_files.txt
) else (
    echo 未找到JavaScript文件引用
)

echo.
echo 🔍 查找表单提交地址...
findstr /i "action=" page_content.html > forms.txt 2>nul
if exist forms.txt (
    echo 找到表单:
    type forms.txt
) else (
    echo 未找到表单提交地址
)

echo.
echo ========================================
echo    建议的下一步
echo ========================================
echo.
echo 1. 📞 联系平台客服获取API文档
echo 2. 🔐 申请试用账号登录查看后台
echo 3. 🌐 使用浏览器F12分析网络请求
echo 4. 📱 使用抓包工具分析API调用
echo.

pause
