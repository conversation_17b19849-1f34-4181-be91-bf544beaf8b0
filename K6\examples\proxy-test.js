import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter, Gauge } from 'k6/metrics';

// 自定义指标
const errorRate = new Rate('custom_error_rate');
const responseTime = new Trend('custom_response_time');
const requestCount = new Counter('custom_request_count');
const activeUsers = new Gauge('active_users');

// 从环境变量获取配置
const TARGET_URL = __ENV.TARGET_URL || __ENV.TEST_URL || 'https://httpbin.org/get';
const VUS = parseInt(__ENV.VUS) || 10;
const DURATION = __ENV.DURATION || '30s';
const REQUEST_METHOD = __ENV.REQUEST_METHOD || 'GET';
const REQUEST_DATA = __ENV.REQUEST_DATA || '{}';

// 场景配置
const SCENARIO = __ENV.SCENARIO || 'simple';
const MAX_VUS = parseInt(__ENV.MAX_VUS) || VUS;
const SPIKE_VUS = parseInt(__ENV.SPIKE_VUS) || VUS * 2;

// 代理配置 (从环境变量获取)
const PROXY_HOST = __ENV.PROXY_HOST || '';
const PROXY_PORT = __ENV.PROXY_PORT || '';
const PROXY_USERNAME = __ENV.PROXY_USERNAME || '';
const PROXY_PASSWORD = __ENV.PROXY_PASSWORD || '';

// 测试配置
// 根据场景类型配置测试选项
function getScenarioOptions() {
    const baseThresholds = {
        'custom_error_rate': ['rate<0.05'],      // 错误率小于5%
        'custom_response_time': ['p(95)<400'],   // 95%响应时间小于400ms
        'http_req_duration': ['p(95)<500', 'p(99)<1000'], // 响应时间阈值
        'http_req_failed': ['rate<0.1'],         // 失败率小于10%
    };

    switch (SCENARIO) {
        case 'gradual':
            return {
                stages: [
                    { duration: '1m', target: Math.floor(MAX_VUS * 0.2) },  // 预热到20%
                    { duration: '2m', target: Math.floor(MAX_VUS * 0.5) },  // 增加到50%
                    { duration: '2m', target: MAX_VUS },                    // 达到最大值
                    { duration: '2m', target: Math.floor(MAX_VUS * 0.5) },  // 降到50%
                    { duration: '1m', target: 0 },                         // 冷却
                ],
                thresholds: baseThresholds,
            };

        case 'spike':
            return {
                stages: [
                    { duration: '30s', target: VUS },           // 正常负载
                    { duration: '30s', target: SPIKE_VUS },     // 突然峰值
                    { duration: '1m', target: VUS },            // 回到正常
                    { duration: '30s', target: SPIKE_VUS },     // 再次峰值
                    { duration: '30s', target: 0 },            // 结束
                ],
                thresholds: {
                    ...baseThresholds,
                    'http_req_duration': ['p(95)<1000', 'p(99)<2000'], // 峰值测试放宽阈值
                },
            };

        case 'endurance':
            return {
                stages: [
                    { duration: '1m', target: VUS },            // 快速达到目标
                    { duration: DURATION, target: VUS },       // 长时间稳定运行
                    { duration: '1m', target: 0 },             // 结束
                ],
                thresholds: baseThresholds,
            };

        default: // simple
            return {
                vus: VUS,
                duration: DURATION,
                thresholds: baseThresholds,
            };
    }
}

export let options = {
    ...getScenarioOptions(),
    

    
    // 输出配置
    summaryTrendStats: ['avg', 'min', 'med', 'max', 'p(90)', 'p(95)', 'p(99)'],
    
    // HTML报告配置
    ext: {
        loadimpact: {
            distribution: {
                'amazon:us:ashburn': { loadZone: 'amazon:us:ashburn', percent: 100 },
            },
        },
    },
};

// 设置函数（测试开始前执行）
export function setup() {
    console.log('🚀 K6代理测试开始');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📊 测试配置:');
    console.log(`   🎯 目标URL: ${TARGET_URL}`);
    console.log(`   📡 请求方法: ${REQUEST_METHOD}`);
    console.log(`   🎭 测试场景: ${SCENARIO}`);
    console.log(`   👥 虚拟用户: ${VUS}${SCENARIO !== 'simple' ? ` (最大: ${MAX_VUS})` : ''}`);
    console.log(`   ⏱️  测试时长: ${DURATION}`);
    
    if (PROXY_HOST && PROXY_PORT) {
        console.log('🌐 代理配置:');
        console.log(`   📡 代理类型: SOCKS5`);
        console.log(`   🌐 代理地址: ${PROXY_HOST}:${PROXY_PORT}`);
        console.log(`   🔐 身份验证: ${PROXY_USERNAME ? '已启用' : '未启用'}`);
    } else {
        console.log('🌐 代理配置: 未启用');
    }
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    // 执行连通性测试
    console.log('🔍 执行连通性测试...');
    try {
        let testParams = {};

        // 如果配置了代理，在连通性测试中也使用代理
        if (PROXY_HOST && PROXY_PORT) {
            if (PROXY_USERNAME && PROXY_PASSWORD) {
                testParams.proxy = `socks5://${PROXY_USERNAME}:${PROXY_PASSWORD}@${PROXY_HOST}:${PROXY_PORT}`;
            } else {
                testParams.proxy = `socks5://${PROXY_HOST}:${PROXY_PORT}`;
            }
            console.log(`🌐 使用代理进行连通性测试: ${PROXY_HOST}:${PROXY_PORT}`);
        }

        let testResponse = http.get(TARGET_URL, testParams);
        if (testResponse.status === 200) {
            console.log(`✅ 目标服务可访问 (${testResponse.status})`);
            if (PROXY_HOST && PROXY_PORT) {
                console.log(`✅ 代理连接正常工作`);
            }
        } else {
            console.log(`⚠️  目标服务响应异常 (${testResponse.status})`);
        }
    } catch (error) {
        console.log(`❌ 连通性测试失败: ${error}`);
        if (PROXY_HOST && PROXY_PORT) {
            console.log(`❌ 可能是代理配置问题，请检查代理设置`);
        }
    }
    
    return { 
        startTime: new Date(),
        targetUrl: TARGET_URL,
        method: REQUEST_METHOD
    };
}

// 主测试函数
export default function(data) {
    // 更新活跃用户数
    activeUsers.add(1);
    
    let response;
    let requestData;
    
    try {
        // 解析请求数据
        if (REQUEST_METHOD !== 'GET' && REQUEST_DATA) {
            try {
                requestData = JSON.parse(REQUEST_DATA);
            } catch (e) {
                requestData = { data: REQUEST_DATA };
            }
        }
        
        // 准备HTTP请求参数
        const startTime = Date.now();

        // 构建请求参数（包含代理配置）
        let requestParams = {
            headers: { 'Content-Type': 'application/json' }
        };

        // 如果配置了代理，添加代理设置
        if (PROXY_HOST && PROXY_PORT) {
            // K6代理配置方式
            requestParams.proxy = `socks5://${PROXY_HOST}:${PROXY_PORT}`;

            // 如果有认证信息，添加到代理URL中
            if (PROXY_USERNAME && PROXY_PASSWORD) {
                requestParams.proxy = `socks5://${PROXY_USERNAME}:${PROXY_PASSWORD}@${PROXY_HOST}:${PROXY_PORT}`;
            }
        }

        // 发送HTTP请求
        switch (REQUEST_METHOD.toUpperCase()) {
            case 'GET':
                response = http.get(TARGET_URL, requestParams);
                break;
            case 'POST':
                response = http.post(TARGET_URL, JSON.stringify(requestData), requestParams);
                break;
            case 'PUT':
                response = http.put(TARGET_URL, JSON.stringify(requestData), requestParams);
                break;
            case 'DELETE':
                response = http.del(TARGET_URL, requestParams);
                break;
            default:
                response = http.get(TARGET_URL, requestParams);
        }
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // 记录自定义指标
        responseTime.add(duration);
        requestCount.add(1);
        
        // 详细的响应检查
        const checks = check(response, {
            '✅ HTTP状态码正常': (r) => r.status >= 200 && r.status < 300,
            '⚡ 响应时间合理': (r) => r.timings.duration < 2000,
            '📦 响应体存在': (r) => r.body && r.body.length > 0,
            '🔗 连接建立成功': (r) => r.timings.connecting >= 0,
            '📡 DNS解析成功': (r) => r.timings.dns_lookup >= 0,
        });
        
        // 记录错误率
        errorRate.add(!checks);
        
        // 成功请求的日志
        if (response.status >= 200 && response.status < 300) {
            if (Math.random() < 0.1) { // 10%的概率输出日志，避免日志过多
                console.log(`✅ 请求成功: ${REQUEST_METHOD} ${TARGET_URL} - ${response.status} (${duration}ms)`);
            }
        } else {
            console.log(`❌ 请求失败: ${REQUEST_METHOD} ${TARGET_URL} - ${response.status} (${duration}ms)`);
            console.log(`   响应体: ${response.body ? response.body.substring(0, 200) : 'N/A'}`);
        }
        
    } catch (error) {
        console.log(`💥 请求异常: ${error}`);
        errorRate.add(1);
        requestCount.add(1);
    }
    
    // 更新活跃用户数
    activeUsers.add(-1);
    
    // 模拟用户思考时间 (随机1-3秒)
    sleep(Math.random() * 2 + 1);
}

// 清理函数（测试结束后执行）
export function teardown(data) {
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('🎉 测试完成!');
    
    const endTime = new Date();
    const totalTime = endTime - data.startTime;
    const totalSeconds = Math.round(totalTime / 1000);
    
    console.log(`⏱️  总耗时: ${totalTime}ms (${totalSeconds}秒)`);
    console.log(`📅 测试时间: ${data.startTime} - ${endTime}`);
    console.log('📊 详细报告请查看测试结果');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
}

// 处理测试中断
export function handleSummary(data) {
    return {
        'summary.html': htmlReport(data),
        'summary.json': JSON.stringify(data, null, 2),
    };
}

// 生成HTML报告
function htmlReport(data) {
    const proxyInfo = PROXY_HOST && PROXY_PORT ? 
        `<p><strong>🌐 代理配置:</strong> SOCKS5 ${PROXY_HOST}:${PROXY_PORT} ${PROXY_USERNAME ? '(已认证)' : '(无认证)'}</p>` : 
        '<p><strong>🌐 代理配置:</strong> 未启用</p>';
        
    return `
<!DOCTYPE html>
<html>
<head>
    <title>K6代理测试报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #4CAF50; color: white; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { color: #4CAF50; }
        .warning { color: #FF9800; }
        .error { color: #F44336; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 K6代理性能测试报告</h1>
        <p>测试时间: ${new Date().toLocaleString()}</p>
    </div>
    
    <div class="section">
        <h2>📋 测试配置</h2>
        <p><strong>🎯 目标URL:</strong> ${TARGET_URL}</p>
        <p><strong>📡 请求方法:</strong> ${REQUEST_METHOD}</p>
        <p><strong>👥 虚拟用户:</strong> ${VUS}</p>
        <p><strong>⏱️ 测试时长:</strong> ${DURATION}</p>
        ${proxyInfo}
    </div>
    
    <div class="section">
        <h2>📊 性能指标</h2>
        <div class="metric">
            <strong>总请求数:</strong> ${data.metrics.http_reqs ? data.metrics.http_reqs.count : 'N/A'}
        </div>
        <div class="metric">
            <strong>平均响应时间:</strong> ${data.metrics.http_req_duration ? Math.round(data.metrics.http_req_duration.avg) : 'N/A'}ms
        </div>
        <div class="metric">
            <strong>成功率:</strong> ${data.metrics.http_req_failed ? Math.round((1 - data.metrics.http_req_failed.rate) * 100) : 'N/A'}%
        </div>
    </div>
    
    <div class="section">
        <h2>🎯 测试结论</h2>
        <p>测试已完成，详细数据请查看上方指标。</p>
        ${PROXY_HOST && PROXY_PORT ? '<p class="success">✅ 代理连接正常工作</p>' : '<p>ℹ️ 未使用代理连接</p>'}
    </div>
</body>
</html>`;
}
