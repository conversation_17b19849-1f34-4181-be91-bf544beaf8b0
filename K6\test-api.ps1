# API指纹识别测试脚本
$baseUrl = "http://**************:8394"

Write-Host "API指纹识别工具 - 确定真实短信服务商" -ForegroundColor Yellow
Write-Host "=" * 50 -ForegroundColor Yellow

# 测试登录页面
Write-Host "测试登录页面..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/loginBlue.html" -Method GET -TimeoutSec 10
    Write-Host "登录页面状态: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "内容类型: $($response.Headers['Content-Type'])" -ForegroundColor White
    if ($response.Headers['Server']) {
        Write-Host "服务器: $($response.Headers['Server'])" -ForegroundColor White
    }
    if ($response.Headers['X-Powered-By']) {
        Write-Host "技术栈: $($response.Headers['X-Powered-By'])" -ForegroundColor White
    }
    Write-Host "内容长度: $($response.Content.Length)" -ForegroundColor White
} catch {
    Write-Host "登录页面测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试验证码接口
Write-Host "测试验证码接口..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/code.aspx" -Method GET -TimeoutSec 10
    Write-Host "验证码接口状态: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "内容类型: $($response.Headers['Content-Type'])" -ForegroundColor White
    Write-Host "内容长度: $($response.Content.Length)" -ForegroundColor White
} catch {
    Write-Host "验证码接口测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试登录接口
Write-Host "测试登录接口..." -ForegroundColor Cyan
try {
    $loginData = "account=test&password=test&code=1234&remembermeclient=on"
    $response = Invoke-WebRequest -Uri "$baseUrl/login" -Method POST -Body $loginData -ContentType "application/x-www-form-urlencoded" -TimeoutSec 10
    Write-Host "登录接口状态: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "内容类型: $($response.Headers['Content-Type'])" -ForegroundColor White
    
    # 尝试解析JSON响应
    try {
        $jsonResponse = $response.Content | ConvertFrom-Json
        Write-Host "JSON响应字段: $($jsonResponse.PSObject.Properties.Name -join ', ')" -ForegroundColor Yellow
        Write-Host "响应内容: $($response.Content)" -ForegroundColor White
    } catch {
        Write-Host "非JSON响应，内容预览: $($response.Content.Substring(0, [Math]::Min(200, $response.Content.Length)))" -ForegroundColor White
    }
} catch {
    Write-Host "登录接口测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试可能的API端点
$endpoints = @("/api/sms/send", "/api/send", "/sms/send", "/send", "/sendSms")

foreach ($endpoint in $endpoints) {
    Write-Host "测试端点: $endpoint" -ForegroundColor Cyan
    
    # 测试JSON格式
    try {
        $jsonData = '{"phone":"13800138000","content":"test","mobile":"13800138000","msg":"test"}'
        $response = Invoke-WebRequest -Uri "$baseUrl$endpoint" -Method POST -Body $jsonData -ContentType "application/json" -TimeoutSec 10
        Write-Host "  JSON格式 - 状态: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "  内容类型: $($response.Headers['Content-Type'])" -ForegroundColor White
        
        try {
            $jsonResponse = $response.Content | ConvertFrom-Json
            Write-Host "  JSON字段: $($jsonResponse.PSObject.Properties.Name -join ', ')" -ForegroundColor Yellow
            Write-Host "  响应: $($response.Content)" -ForegroundColor White
        } catch {
            Write-Host "  响应预览: $($response.Content.Substring(0, [Math]::Min(100, $response.Content.Length)))" -ForegroundColor White
        }
    } catch {
        Write-Host "  JSON格式失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 测试表单格式
    try {
        $formData = "phone=13800138000&content=test&mobile=13800138000&msg=test"
        $response = Invoke-WebRequest -Uri "$baseUrl$endpoint" -Method POST -Body $formData -ContentType "application/x-www-form-urlencoded" -TimeoutSec 10
        Write-Host "  表单格式 - 状态: $($response.StatusCode)" -ForegroundColor Green
        
        try {
            $jsonResponse = $response.Content | ConvertFrom-Json
            Write-Host "  JSON字段: $($jsonResponse.PSObject.Properties.Name -join ', ')" -ForegroundColor Yellow
        } catch {
            Write-Host "  非JSON响应" -ForegroundColor Gray
        }
    } catch {
        Write-Host "  表单格式失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

# 服务商特征分析
Write-Host "服务商特征分析:" -ForegroundColor Yellow
Write-Host "基于发现的技术栈和响应特征:" -ForegroundColor White

$findings = @()

# 检查ASP.NET特征
if ($response.Headers['X-Powered-By'] -like "*ASP.NET*") {
    $findings += "ASP.NET技术栈 - 符合创蓝253、云片网络特征"
}

# 检查IIS特征
if ($response.Headers['Server'] -like "*IIS*") {
    $findings += "IIS服务器 - 符合Windows部署的短信平台"
}

# 检查端口特征
$findings += "端口8394 - 非标准端口，可能是代理或中转服务"

foreach ($finding in $findings) {
    Write-Host "  • $finding" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "结论:" -ForegroundColor Yellow
Write-Host "  1. 这是一个基于ASP.NET的短信代理平台" -ForegroundColor White
Write-Host "  2. 背后很可能使用云片网络、创蓝253等服务商的API" -ForegroundColor White
Write-Host "  3. 需要申请测试账号进行实际API调用来确定具体服务商" -ForegroundColor White
Write-Host "  4. 建议通过发送实际短信来分析短信特征" -ForegroundColor White
