# 🔍 短信API分析完整指南

## 🎯 目标网站: http://**************:8394/loginBlue.html

---

## 📋 **方法1: 浏览器开发者工具分析** (立即可用)

### **步骤1: 打开开发者工具**
1. 打开Chrome/Edge浏览器
2. 按 `F12` 或右键 → 检查
3. 切换到 `Network` (网络) 标签
4. 勾选 `Preserve log` (保留日志)

### **步骤2: 访问目标网站**
```
访问: http://**************:8394/loginBlue.html
```

### **步骤3: 分析网络请求**
观察以下类型的请求：
- **登录请求**: 通常是 POST 到 `/login` 或类似路径
- **API调用**: 查找包含 `api`、`sms`、`send` 的URL
- **认证请求**: 查找获取token或session的请求

### **步骤4: 记录关键信息**
```
请求URL: _______________
请求方法: GET/POST
请求头: 
  - Content-Type: _______________
  - Authorization: _______________
请求参数:
  - phone: _______________
  - message: _______________
  - sign: _______________
响应格式:
  - 成功: {"code": 200, "msg": "success"}
  - 失败: {"code": 400, "msg": "error"}
```

---

## 📋 **方法2: 使用Fiddler分析** (推荐)

### **安装Fiddler**
1. 下载: https://www.telerik.com/fiddler
2. 安装并启动
3. 配置HTTPS解密

### **配置步骤**
1. Tools → Options → HTTPS
2. 勾选 "Capture HTTPS CONNECTs"
3. 勾选 "Decrypt HTTPS traffic"
4. 安装证书

### **分析流程**
1. 启动Fiddler
2. 访问目标网站
3. 在左侧列表查看所有请求
4. 双击请求查看详细信息

---

## 📋 **方法3: 手动API测试**

### **常见短信API端点猜测**
```bash
# 可能的API端点
http://**************:8394/api/sms/send
http://**************:8394/sms/send
http://**************:8394/send
http://**************:8394/api/send
http://**************:8394/sendSms
```

### **使用curl测试**
```bash
# 测试GET请求
curl -X GET "http://**************:8394/api/sms/send"

# 测试POST请求
curl -X POST "http://**************:8394/api/sms/send" \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","message":"test"}'

# 测试表单提交
curl -X POST "http://**************:8394/api/sms/send" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "phone=13800138000&message=test&sign=yoursign"
```

---

## 📋 **方法4: 源码分析**

### **查看页面源码**
1. 右键 → 查看页面源代码
2. 搜索关键词:
   - `api`
   - `sms`
   - `send`
   - `ajax`
   - `fetch`
   - `XMLHttpRequest`

### **查找JavaScript文件**
```javascript
// 常见的API调用模式
$.ajax({
    url: '/api/sms/send',
    method: 'POST',
    data: {
        phone: phone,
        message: message,
        sign: sign
    }
});

// 或者
fetch('/api/sms/send', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        phone: phone,
        message: message
    })
});
```

---

## 🎯 **分析结果记录模板**

### **API基本信息**
```
基础URL: http://**************:8394
API端点: /api/sms/send
请求方法: POST
认证方式: API Key / Token / Session
```

### **请求格式**
```json
{
    "phone": "13800138000",
    "message": "您的验证码是123456",
    "sign": "您的签名",
    "template_id": "模板ID",
    "api_key": "您的API密钥"
}
```

### **响应格式**
```json
// 成功响应
{
    "code": 200,
    "message": "发送成功",
    "data": {
        "msg_id": "消息ID",
        "cost": "费用"
    }
}

// 失败响应
{
    "code": 400,
    "message": "发送失败",
    "error": "错误详情"
}
```

---

## 🚀 **立即开始分析**

### **推荐步骤**:
1. **🌐 浏览器分析**: 使用F12开发者工具 (5分钟)
2. **📝 记录信息**: 填写上述模板 (5分钟)  
3. **🧪 API测试**: 使用curl或Postman测试 (10分钟)
4. **💻 编写代码**: 根据分析结果编写对接代码 (15分钟)

### **需要帮助时**:
- 分享开发者工具截图
- 提供网络请求详情
- 我将帮您分析API调用模式

**现在就开始分析吧！打开浏览器，按F12，访问目标网站！** 🎯
