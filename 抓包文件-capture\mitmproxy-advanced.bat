@echo off
chcp 65001 >nul
title mitmproxy 高级Web界面

echo ========================================
echo    mitmproxy 高级Web可视化界面
echo ========================================
echo.

:menu
echo 🎯 选择启动模式:
echo.
echo 1. 🌐 标准Web界面 (端口8081)
echo 2. 🚀 增强Web界面 (带插件)
echo 3. 📱 移动端优化界面
echo 4. 🔧 自定义配置启动
echo 5. 📊 查看抓包日志
echo 6. 🧹 清理日志文件
echo 0. ❌ 退出
echo.

set /p choice=请选择模式 (0-6): 

if "%choice%"=="0" goto :end
if "%choice%"=="1" goto :standard
if "%choice%"=="2" goto :enhanced
if "%choice%"=="3" goto :mobile
if "%choice%"=="4" goto :custom
if "%choice%"=="5" goto :viewlogs
if "%choice%"=="6" goto :cleanlogs

echo ❌ 无效选择，请重试
pause
goto :menu

:standard
echo.
echo 🌐 启动标准Web界面...
echo 📱 代理地址: %COMPUTERNAME%:8080
echo 🌐 Web界面: http://127.0.0.1:8081
echo.
cd /d "E:\mitmproxy-setup"
mitmweb --web-host 0.0.0.0 --web-port 8081 --listen-port 8080 --set confdir=E:\mitmproxy-setup
goto :end

:enhanced
echo.
echo 🚀 启动增强Web界面 (带日志插件)...
echo 📱 代理地址: %COMPUTERNAME%:8080
echo 🌐 Web界面: http://127.0.0.1:8081
echo 📊 日志文件: E:\mitmproxy-setup\logs\requests.json
echo.

REM 复制插件到正确位置
if not exist "E:\mitmproxy-setup\scripts" mkdir "E:\mitmproxy-setup\scripts"
copy /y "mitmproxy-addon.py" "E:\mitmproxy-setup\scripts\addon.py" >nul

cd /d "E:\mitmproxy-setup"
mitmweb --web-host 0.0.0.0 --web-port 8081 --listen-port 8080 --set confdir=E:\mitmproxy-setup -s scripts/addon.py
goto :end

:mobile
echo.
echo 📱 启动移动端优化界面...
echo 📱 代理地址: %COMPUTERNAME%:8080
echo 🌐 Web界面: http://127.0.0.1:8082 (移动端优化)
echo.
cd /d "E:\mitmproxy-setup"
mitmweb --web-host 0.0.0.0 --web-port 8082 --listen-port 8080 --set confdir=E:\mitmproxy-setup --set web_iface=0.0.0.0
goto :end

:custom
echo.
echo 🔧 自定义配置启动
echo.
set /p web_port=Web界面端口 (默认8081): 
set /p proxy_port=代理端口 (默认8080): 
set /p enable_plugin=启用日志插件? (y/n, 默认n): 

if "%web_port%"=="" set web_port=8081
if "%proxy_port%"=="" set proxy_port=8080

echo.
echo 🚀 启动自定义配置...
echo 📱 代理地址: %COMPUTERNAME%:%proxy_port%
echo 🌐 Web界面: http://127.0.0.1:%web_port%
echo.

cd /d "E:\mitmproxy-setup"

if /i "%enable_plugin%"=="y" (
    if not exist "E:\mitmproxy-setup\scripts" mkdir "E:\mitmproxy-setup\scripts"
    copy /y "mitmproxy-addon.py" "E:\mitmproxy-setup\scripts\addon.py" >nul
    mitmweb --web-host 0.0.0.0 --web-port %web_port% --listen-port %proxy_port% --set confdir=E:\mitmproxy-setup -s scripts/addon.py
) else (
    mitmweb --web-host 0.0.0.0 --web-port %web_port% --listen-port %proxy_port% --set confdir=E:\mitmproxy-setup
)
goto :end

:viewlogs
echo.
echo 📊 查看抓包日志...
echo.
if exist "E:\mitmproxy-setup\logs\requests.json" (
    echo 📁 日志文件位置: E:\mitmproxy-setup\logs\requests.json
    echo 📊 文件大小: 
    for %%A in ("E:\mitmproxy-setup\logs\requests.json") do echo    %%~zA 字节
    echo.
    echo 🔍 最近5条记录:
    powershell -Command "Get-Content 'E:\mitmproxy-setup\logs\requests.json' | Select-Object -Last 5"
) else (
    echo ❌ 未找到日志文件
    echo 💡 请先使用增强模式运行一次抓包
)
echo.
pause
goto :menu

:cleanlogs
echo.
echo 🧹 清理日志文件...
if exist "E:\mitmproxy-setup\logs\requests.json" (
    del "E:\mitmproxy-setup\logs\requests.json"
    echo ✅ 日志文件已清理
) else (
    echo ℹ️  没有日志文件需要清理
)
echo.
pause
goto :menu

:end
echo.
echo 👋 感谢使用 mitmproxy Web界面！
pause
