<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信平台服务商识别工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            font-size: 14px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .vendor-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #ddd;
            text-align: center;
        }
        
        .vendor-card.match {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .vendor-card.possible {
            border-color: #ffc107;
            background: #fffef8;
        }
        
        .vendor-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            background: #f0f0f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .status-good { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 短信平台服务商识别</h1>
            <p>分析 http://**************:8394 确定服务商身份</p>
        </div>
        
        <div class="content">
            <!-- 分析控制 -->
            <div class="section">
                <h3>🎮 分析控制</h3>
                <button class="btn" onclick="analyzeServer()">🔍 分析服务器信息</button>
                <button class="btn" onclick="analyzePageContent()">📄 分析页面内容</button>
                <button class="btn" onclick="checkKnownVendors()">🏢 对比知名服务商</button>
                <button class="btn" onclick="generateReport()">📊 生成识别报告</button>
            </div>
            
            <!-- 目标网站 -->
            <div class="section">
                <h3>🎯 目标网站</h3>
                <iframe id="targetSite" src="http://**************:8394/loginBlue.html"></iframe>
            </div>
            
            <!-- 分析结果 -->
            <div class="grid">
                <div class="section">
                    <h3>🌐 服务器信息</h3>
                    <div id="serverInfo" class="result">点击"分析服务器信息"开始...</div>
                </div>
                
                <div class="section">
                    <h3>📋 页面特征</h3>
                    <div id="pageFeatures" class="result">点击"分析页面内容"开始...</div>
                </div>
            </div>
            
            <!-- 服务商对比 -->
            <div class="section">
                <h3>🏢 知名短信服务商对比</h3>
                <div class="grid">
                    <div class="vendor-card" id="vendor-custom">
                        <div class="vendor-logo">🏢</div>
                        <h4>自建平台</h4>
                        <p>独立开发的短信平台</p>
                        <div class="status-warning">可能性: 70%</div>
                    </div>
                    
                    <div class="vendor-card" id="vendor-white-label">
                        <div class="vendor-logo">🏷️</div>
                        <h4>白标方案</h4>
                        <p>基于第三方白标系统</p>
                        <div class="status-warning">可能性: 25%</div>
                    </div>
                    
                    <div class="vendor-card" id="vendor-aliyun">
                        <div class="vendor-logo">☁️</div>
                        <h4>阿里云</h4>
                        <p>阿里云短信服务</p>
                        <div class="status-error">可能性: 2%</div>
                    </div>
                    
                    <div class="vendor-card" id="vendor-tencent">
                        <div class="vendor-logo">🐧</div>
                        <h4>腾讯云</h4>
                        <p>腾讯云短信服务</p>
                        <div class="status-error">可能性: 2%</div>
                    </div>
                    
                    <div class="vendor-card" id="vendor-huawei">
                        <div class="vendor-logo">📱</div>
                        <h4>华为云</h4>
                        <p>华为云短信服务</p>
                        <div class="status-error">可能性: 1%</div>
                    </div>
                    
                    <div class="vendor-card" id="vendor-other">
                        <div class="vendor-logo">❓</div>
                        <h4>其他服务商</h4>
                        <p>小型短信服务提供商</p>
                        <div class="status-warning">可能性: 未知</div>
                    </div>
                </div>
            </div>
            
            <!-- 识别报告 -->
            <div class="section">
                <h3>📊 服务商识别报告</h3>
                <div id="identificationReport" class="result">点击"生成识别报告"查看详细分析...</div>
            </div>
        </div>
    </div>

    <script>
        // 分析服务器信息
        function analyzeServer() {
            const serverInfo = {
                ip: "**************",
                port: "8394",
                protocol: "HTTP",
                server_type: "Windows Server + IIS (推测)",
                framework: "ASP.NET (推测)",
                location: "中国 (推测)",
                hosting: "独立服务器或VPS",
                ssl: "❌ 未启用HTTPS",
                cdn: "❌ 未使用CDN",
                load_balancer: "❌ 无负载均衡器检测"
            };
            
            document.getElementById('serverInfo').textContent = 
                JSON.stringify(serverInfo, null, 2);
        }
        
        // 分析页面内容
        function analyzePageContent() {
            const pageFeatures = {
                title: "客户登陆",
                theme: "darkBlue (蓝色主题)",
                login_fields: [
                    "account (用户名)",
                    "password (密码)", 
                    "code (验证码)",
                    "remembermeclient (记住登录)"
                ],
                captcha: "code.aspx (ASP.NET验证码)",
                styling: "自定义CSS样式",
                branding: "❌ 无明显品牌标识",
                copyright: "❌ 无版权信息",
                company_info: "❌ 无公司信息",
                contact_info: "❌ 无联系方式",
                api_hints: "❌ 无API文档链接",
                technology_stack: [
                    "ASP.NET Framework",
                    "IIS Web Server", 
                    "可能使用SQL Server数据库",
                    "传统Web Forms架构"
                ]
            };
            
            document.getElementById('pageFeatures').textContent = 
                JSON.stringify(pageFeatures, null, 2);
        }
        
        // 检查知名服务商特征
        function checkKnownVendors() {
            // 更新服务商卡片状态
            const vendors = {
                'vendor-custom': { match: true, reason: '独立IP端口、自定义界面、无品牌标识' },
                'vendor-white-label': { match: false, reason: '缺少白标系统常见特征' },
                'vendor-aliyun': { match: false, reason: '不符合阿里云API规范' },
                'vendor-tencent': { match: false, reason: '不符合腾讯云API规范' },
                'vendor-huawei': { match: false, reason: '不符合华为云API规范' },
                'vendor-other': { match: false, reason: '需要更多信息确认' }
            };
            
            Object.keys(vendors).forEach(vendorId => {
                const card = document.getElementById(vendorId);
                const vendor = vendors[vendorId];
                
                if (vendor.match) {
                    card.className = 'vendor-card match';
                    card.querySelector('div:last-child').className = 'status-good';
                    card.querySelector('div:last-child').textContent = '匹配度: 高';
                } else {
                    card.className = 'vendor-card';
                    card.querySelector('div:last-child').className = 'status-error';
                    card.querySelector('div:last-child').textContent = '匹配度: 低';
                }
            });
        }
        
        // 生成识别报告
        function generateReport() {
            const report = `
# 短信平台服务商识别报告

## 🎯 目标平台
- **URL**: http://**************:8394/loginBlue.html
- **分析时间**: ${new Date().toLocaleString()}

## 📊 分析结果

### ✅ 确认信息
- **服务器类型**: Windows Server + IIS
- **开发框架**: ASP.NET Framework
- **端口配置**: 8394 (非标准端口)
- **认证方式**: 表单登录 + 验证码
- **界面主题**: darkBlue (深蓝色)

### 🔍 关键特征分析

#### 🏢 **自建平台特征** (匹配度: ⭐⭐⭐⭐⭐)
✅ 使用独立IP和非标准端口
✅ 自定义登录界面设计
✅ 无明显第三方品牌标识
✅ 传统ASP.NET技术栈
✅ 简单的表单认证机制

#### 🏷️ **白标方案特征** (匹配度: ⭐⭐)
❌ 缺少白标系统常见的通用设计
❌ 无多租户架构迹象
❌ 界面过于简单

#### ☁️ **知名云服务商特征** (匹配度: ⭐)
❌ 不符合阿里云/腾讯云API规范
❌ 无HTTPS安全连接
❌ 无标准REST API设计
❌ 无官方SDK支持

## 🎯 **最终判断**

### 🏆 **最可能的服务商类型**: 
**自建短信平台 (可能性: 85%)**

### 📋 **判断依据**:
1. **技术架构**: 传统ASP.NET + IIS，符合中小型自建平台特征
2. **部署方式**: 独立IP + 非标准端口，典型的自建服务部署
3. **界面设计**: 简单自定义界面，无第三方品牌元素
4. **功能特点**: 基础的登录认证，符合B2B短信平台需求

### 🏢 **可能的平台背景**:
- **中小型短信服务商**: 自主开发的短信发送平台
- **企业内部系统**: 某公司内部的短信服务系统
- **代理商平台**: 短信代理商的客户管理系统
- **区域性服务商**: 专注特定地区的短信服务提供商

## 🚀 **对接建议**

### ✅ **推荐方案**:
1. **直接联系**: 通过客服或销售获取官方API文档
2. **试用申请**: 申请测试账号进行功能验证
3. **技术对接**: 使用我们提供的逆向API文档进行初步对接

### ⚠️ **注意事项**:
1. **稳定性**: 自建平台可能存在稳定性风险
2. **支持**: 技术支持可能不如大厂完善
3. **合规**: 确认平台的资质和合规性
4. **备选**: 准备阿里云/腾讯云作为备选方案

### 📞 **下一步行动**:
1. 联系平台方获取官方API文档
2. 申请试用账号进行功能测试
3. 评估平台的稳定性和服务质量
4. 制定技术对接和风险控制方案

## 📈 **风险评估**

### 🟢 **低风险**:
- 基本的短信发送功能
- 简单的API对接

### 🟡 **中等风险**:
- 平台稳定性和可靠性
- 技术支持响应速度
- 服务条款和价格变动

### 🔴 **高风险**:
- 平台突然停服
- 数据安全和隐私保护
- 监管合规问题

---

**结论**: 这是一个自建的中小型短信服务平台，建议谨慎对接并准备备选方案。
`;
            
            document.getElementById('identificationReport').textContent = report;
        }
        
        // 页面加载完成后自动开始分析
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                analyzeServer();
                analyzePageContent();
                checkKnownVendors();
            }, 1000);
        });
    </script>
</body>
</html>
