<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信API测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .quick-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 短信API测试工具</h1>
            <p>分析和测试 http://175.27.145.189:8394 的API接口</p>
        </div>
        
        <div class="content">
            <!-- API配置区域 -->
            <div class="section">
                <h3>🔧 API配置</h3>
                <div class="form-group">
                    <label for="baseUrl">基础URL:</label>
                    <input type="text" id="baseUrl" value="http://175.27.145.189:8394" placeholder="http://175.27.145.189:8394">
                </div>
                <div class="form-group">
                    <label for="endpoint">API端点:</label>
                    <input type="text" id="endpoint" value="/api/sms/send" placeholder="/api/sms/send">
                </div>
                <div class="form-group">
                    <label for="method">请求方法:</label>
                    <select id="method">
                        <option value="GET">GET</option>
                        <option value="POST" selected>POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                    </select>
                </div>
            </div>
            
            <!-- 请求参数区域 -->
            <div class="section">
                <h3>📝 请求参数</h3>
                <div class="form-group">
                    <label for="headers">请求头 (JSON格式):</label>
                    <textarea id="headers" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer your_token"}'>{"Content-Type": "application/json"}</textarea>
                </div>
                <div class="form-group">
                    <label for="body">请求体 (JSON格式):</label>
                    <textarea id="body" placeholder='{"phone": "13800138000", "message": "测试消息", "sign": "您的签名"}'>{"phone": "13800138000", "message": "测试消息", "sign": "测试签名"}</textarea>
                </div>
            </div>
            
            <!-- 快速测试区域 -->
            <div class="section">
                <h3>⚡ 快速测试</h3>
                <p>点击下方按钮快速测试常见的API端点:</p>
                <div class="quick-test">
                    <button class="btn" onclick="quickTest('/api/sms/send')">测试 /api/sms/send</button>
                    <button class="btn" onclick="quickTest('/sms/send')">测试 /sms/send</button>
                    <button class="btn" onclick="quickTest('/send')">测试 /send</button>
                    <button class="btn" onclick="quickTest('/api/send')">测试 /api/send</button>
                    <button class="btn" onclick="quickTest('/sendSms')">测试 /sendSms</button>
                    <button class="btn" onclick="quickTest('/login')">测试 /login</button>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="section">
                <button class="btn" onclick="sendRequest()">🚀 发送请求</button>
                <button class="btn btn-secondary" onclick="clearResult()">🧹 清空结果</button>
                <button class="btn btn-secondary" onclick="exportResult()">📥 导出结果</button>
            </div>
            
            <!-- 结果显示区域 -->
            <div class="section">
                <h3>📊 响应结果</h3>
                <div id="result" class="result info">等待发送请求...</div>
            </div>
        </div>
    </div>

    <script>
        // 发送API请求
        async function sendRequest() {
            const baseUrl = document.getElementById('baseUrl').value;
            const endpoint = document.getElementById('endpoint').value;
            const method = document.getElementById('method').value;
            const headersText = document.getElementById('headers').value;
            const bodyText = document.getElementById('body').value;
            
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在发送请求...';
            
            try {
                // 解析请求头
                let headers = {};
                if (headersText.trim()) {
                    headers = JSON.parse(headersText);
                }
                
                // 构建请求配置
                const config = {
                    method: method,
                    headers: headers,
                    mode: 'cors'
                };
                
                // 添加请求体（如果不是GET请求）
                if (method !== 'GET' && bodyText.trim()) {
                    if (headers['Content-Type'] && headers['Content-Type'].includes('application/json')) {
                        config.body = bodyText;
                    } else {
                        config.body = bodyText;
                    }
                }
                
                const url = baseUrl + endpoint;
                console.log('发送请求到:', url);
                console.log('请求配置:', config);
                
                // 发送请求
                const response = await fetch(url, config);
                
                // 获取响应
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }
                
                // 显示结果
                const result = {
                    url: url,
                    method: method,
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: responseData
                };
                
                resultDiv.className = response.ok ? 'result success' : 'result error';
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败: ${error.message}\n\n注意: 由于CORS限制，可能无法直接从浏览器访问该API。\n建议使用以下方法:\n1. 使用浏览器开发者工具查看网络请求\n2. 使用Postman或curl工具测试\n3. 配置代理服务器绕过CORS限制`;
            }
        }
        
        // 快速测试
        function quickTest(endpoint) {
            document.getElementById('endpoint').value = endpoint;
            sendRequest();
        }
        
        // 清空结果
        function clearResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '等待发送请求...';
        }
        
        // 导出结果
        function exportResult() {
            const result = document.getElementById('result').textContent;
            const blob = new Blob([result], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'api-test-result.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('短信API测试工具已加载');
        });
    </script>
</body>
</html>
