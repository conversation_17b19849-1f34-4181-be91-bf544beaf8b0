# 🚀 mitmproxy Web界面完整安装指南

## ⏱️ **预估完成时间: 15-30分钟**

---

## 📋 **第一步: 安装Python环境** (5-10分钟)

### 方法1: 官方安装 (推荐)
1. 访问 https://www.python.org/downloads/
2. 下载 Python 3.8+ 版本
3. 安装时**勾选 "Add Python to PATH"**
4. 验证安装: 打开CMD输入 `python --version`

### 方法2: 使用Chocolatey
```powershell
# 以管理员身份运行PowerShell
choco install python
```

---

## 📦 **第二步: 安装mitmproxy** (3-5分钟)

```bash
# 安装mitmproxy
pip install mitmproxy

# 验证安装
mitmproxy --version
```

---

## 🏗️ **第三步: 创建E盘目录结构** (1分钟)

```bash
# 创建目录
mkdir E:\mitmproxy-setup
mkdir E:\mitmproxy-setup\scripts
mkdir E:\mitmproxy-setup\certs
mkdir E:\mitmproxy-setup\logs
```

---

## 🚀 **第四步: 启动Web界面** (立即)

### 基础启动
```bash
cd E:\mitmproxy-setup
mitmweb --web-host 0.0.0.0 --web-port 8081 --listen-port 8080
```

### 高级启动 (带配置)
```bash
mitmweb --web-host 0.0.0.0 --web-port 8081 --listen-port 8080 --set confdir=E:\mitmproxy-setup --set web_open_browser=false
```

---

## 📱 **第五步: 手机配置** (5分钟)

### Android配置:
1. 连接与电脑相同的WiFi
2. 设置 → WiFi → 长按网络 → 修改网络
3. 高级选项 → 代理 → 手动
4. 代理主机名: `电脑IP地址`
5. 代理端口: `8080`

### iOS配置:
1. 设置 → WiFi → 点击网络旁的 ⓘ
2. 配置代理 → 手动
3. 服务器: `电脑IP地址`
4. 端口: `8080`

### 证书安装:
1. 手机浏览器访问: `mitm.it`
2. 下载对应系统的证书
3. 安装证书并信任

---

## 🌐 **Web界面功能**

访问 `http://127.0.0.1:8081` 可以看到:

### 📊 **实时监控**
- 实时显示所有HTTP/HTTPS请求
- 请求/响应详细信息
- 请求时间线和统计

### 🔍 **过滤功能**
- 按域名过滤
- 按请求方法过滤
- 按状态码过滤
- 自定义过滤表达式

### 📝 **编辑功能**
- 修改请求内容
- 修改响应内容
- 设置断点调试
- 重放请求

### 💾 **导出功能**
- 导出HAR文件
- 导出curl命令
- 保存请求/响应数据

---

## 🎯 **快速启动脚本**

创建 `E:\mitmproxy-setup\quick-start.bat`:

```batch
@echo off
title mitmproxy Web Interface
echo Starting mitmproxy Web Interface...
echo Web UI: http://127.0.0.1:8081
echo Proxy: %COMPUTERNAME%:8080
echo.
cd /d E:\mitmproxy-setup
mitmweb --web-host 0.0.0.0 --web-port 8081 --listen-port 8080 --set confdir=E:\mitmproxy-setup
```

---

## 🔧 **高级配置**

### 自定义端口
```bash
mitmweb --web-port 9999 --listen-port 9090
```

### 启用HTTPS解密
```bash
mitmweb --set ssl_insecure=true
```

### 保存流量到文件
```bash
mitmweb --save-stream-file E:\mitmproxy-setup\traffic.mitm
```

---

## 🎉 **完成！**

现在您拥有了一个功能完整的Web可视化抓包工具:

- ✅ **实时监控**: 所有APP流量一目了然
- ✅ **Web界面**: 无需命令行，纯图形化操作
- ✅ **完整功能**: 过滤、编辑、重放、导出
- ✅ **跨平台**: 支持Android/iOS设备
- ✅ **专业级**: 媲美Charles、Fiddler的功能

**总用时: 15-30分钟，一次配置，永久使用！** 🎯
