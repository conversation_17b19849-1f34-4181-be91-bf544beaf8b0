# API指纹识别工具 - PowerShell版本
# 确定真实短信服务商

param(
    [string]$BaseUrl = "http://175.27.145.189:8394"
)

# 已知服务商特征库
$VendorSignatures = @{
    "yunpian" = @{
        "error_codes" = @("1", "2", "3", "4", "5")
        "response_fields" = @("code", "msg", "data")
        "api_style" = "restful"
        "typical_errors" = @{
            "1" = "参数错误"
            "2" = "账户余额不足"
            "3" = "手机号格式错误"
        }
    }
    "chuanglan" = @{
        "error_codes" = @("0", "101", "102", "103")
        "response_fields" = @("code", "message", "msgId")
        "api_style" = "soap_like"
        "typical_errors" = @{
            "101" = "账号密码错误"
            "102" = "余额不足"
            "103" = "手机号码格式错误"
        }
    }
    "ronglian" = @{
        "error_codes" = @("000000", "160001", "160002")
        "response_fields" = @("statusCode", "statusMsg", "templateSMS")
        "api_style" = "restful"
        "typical_errors" = @{
            "160001" = "账户信息异常"
            "160002" = "账户余额不足"
        }
    }
    "submail" = @{
        "error_codes" = @("success", "error")
        "response_fields" = @("status", "send_id", "fee")
        "api_style" = "restful"
        "typical_errors" = @{
            "10010" = "用户名或密码错误"
            "10011" = "余额不足"
        }
    }
    "emay" = @{
        "error_codes" = @("0", "1", "2", "3")
        "response_fields" = @("result", "msgid", "custid")
        "api_style" = "custom"
        "typical_errors" = @{
            "1" = "账号密码错误"
            "2" = "余额不足"
        }
    }
}

$Fingerprints = @{}

function Write-Log {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor Cyan
}

function Test-LoginFingerprint {
    Write-Log "🔍 收集登录接口指纹..."
    
    $loginData = @{
        account = "test"
        password = "test"
        code = "1234"
        remembermeclient = "on"
    }
    
    try {
        $response = Invoke-WebRequest -Uri "$BaseUrl/login" -Method POST -Body $loginData -TimeoutSec 10 -ErrorAction Stop
        
        $fingerprint = @{
            endpoint = "/login"
            method = "POST"
            status_code = $response.StatusCode
            headers = $response.Headers
            content_type = $response.Headers["Content-Type"]
            content_length = $response.Content.Length
            timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
        }
        
        # 尝试解析JSON响应
        try {
            $jsonResponse = $response.Content | ConvertFrom-Json
            $fingerprint.response_format = "json"
            $fingerprint.json_fields = $jsonResponse.PSObject.Properties.Name
            $fingerprint.response_data = $jsonResponse
        }
        catch {
            $fingerprint.response_format = "html/text"
            $fingerprint.content_sample = $response.Content.Substring(0, [Math]::Min(200, $response.Content.Length))
        }
        
        $Fingerprints.login = $fingerprint
        Write-Log "✅ 登录接口指纹收集完成"
        
    }
    catch {
        Write-Log "❌ 登录接口测试失败: $($_.Exception.Message)"
        $Fingerprints.login = @{
            endpoint = "/login"
            error = $_.Exception.Message
            timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
        }
    }
}

function Test-SMSFingerprint {
    Write-Log "🔍 收集短信发送接口指纹..."
    
    $endpoints = @("/api/sms/send", "/api/send", "/sms/send", "/send", "/sendSms")
    
    foreach ($endpoint in $endpoints) {
        $testData = @{
            phone = "13800138000"
            content = "测试"
            mobile = "13800138000"
            msg = "测试"
            message = "测试"
        }
        
        $fingerprintKey = "sms_" + $endpoint.Replace("/", "_")
        
        try {
            # 测试JSON格式
            $jsonBody = $testData | ConvertTo-Json
            $response = Invoke-WebRequest -Uri "$BaseUrl$endpoint" -Method POST -Body $jsonBody -ContentType "application/json" -TimeoutSec 10 -ErrorAction Stop
            
            $fingerprint = @{
                endpoint = $endpoint
                method = "POST"
                data_format = "json"
                status_code = $response.StatusCode
                headers = $response.Headers
                content_type = $response.Headers["Content-Type"]
                timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
            }
            
            # 分析响应
            try {
                $jsonResponse = $response.Content | ConvertFrom-Json
                $fingerprint.response_format = "json"
                $fingerprint.json_fields = $jsonResponse.PSObject.Properties.Name
                $fingerprint.response_data = $jsonResponse
                
                # 提取错误码
                $errorCode = Extract-ErrorCode $jsonResponse
                if ($errorCode) {
                    $fingerprint.error_code = $errorCode
                }
            }
            catch {
                $fingerprint.response_format = "html/text"
                $fingerprint.content_sample = $response.Content.Substring(0, [Math]::Min(200, $response.Content.Length))
            }
            
            $Fingerprints[$fingerprintKey] = $fingerprint
            
        }
        catch {
            $Fingerprints[$fingerprintKey] = @{
                endpoint = $endpoint
                error = $_.Exception.Message
                timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
            }
        }
    }
}

function Test-ErrorFingerprints {
    Write-Log "🔍 收集错误响应指纹..."
    
    $errorTests = @(
        @{
            name = "invalid_phone"
            endpoint = "/api/sms/send"
            data = @{ phone = "invalid"; content = "test" }
        },
        @{
            name = "empty_content"
            endpoint = "/api/sms/send"
            data = @{ phone = "13800138000"; content = "" }
        },
        @{
            name = "long_content"
            endpoint = "/api/sms/send"
            data = @{ phone = "13800138000"; content = "x" * 1000 }
        },
        @{
            name = "invalid_endpoint"
            endpoint = "/api/invalid"
            data = @{ test = "test" }
        }
    )
    
    foreach ($test in $errorTests) {
        $fingerprintKey = "error_" + $test.name
        
        try {
            $jsonBody = $test.data | ConvertTo-Json
            $response = Invoke-WebRequest -Uri "$BaseUrl$($test.endpoint)" -Method POST -Body $jsonBody -ContentType "application/json" -TimeoutSec 10 -ErrorAction Stop
            
            $fingerprint = @{
                test_type = $test.name
                endpoint = $test.endpoint
                status_code = $response.StatusCode
                headers = $response.Headers
                timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
            }
            
            try {
                $jsonResponse = $response.Content | ConvertFrom-Json
                $fingerprint.response_format = "json"
                $fingerprint.error_response = $jsonResponse
                
                $errorCode = Extract-ErrorCode $jsonResponse
                $errorMessage = Extract-ErrorMessage $jsonResponse
                
                if ($errorCode) { $fingerprint.error_code = $errorCode }
                if ($errorMessage) { $fingerprint.error_message = $errorMessage }
            }
            catch {
                $fingerprint.response_format = "html/text"
                $fingerprint.content_sample = $response.Content.Substring(0, [Math]::Min(200, $response.Content.Length))
            }
            
            $Fingerprints[$fingerprintKey] = $fingerprint
            
        }
        catch {
            $Fingerprints[$fingerprintKey] = @{
                test_type = $test.name
                endpoint = $test.endpoint
                error = $_.Exception.Message
                timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
            }
        }
    }
}

function Extract-ErrorCode {
    param($response)
    
    $possibleFields = @("code", "error_code", "status", "statusCode", "result", "errno")
    
    foreach ($field in $possibleFields) {
        if ($response.PSObject.Properties.Name -contains $field) {
            return $response.$field
        }
    }
    return $null
}

function Extract-ErrorMessage {
    param($response)
    
    $possibleFields = @("message", "msg", "error", "statusMsg", "error_msg", "desc")
    
    foreach ($field in $possibleFields) {
        if ($response.PSObject.Properties.Name -contains $field) {
            return $response.$field
        }
    }
    return $null
}

function Analyze-VendorMatch {
    Write-Log "🔍 分析服务商匹配度..."
    
    $matches = @{}
    
    foreach ($vendorName in $VendorSignatures.Keys) {
        $signature = $VendorSignatures[$vendorName]
        $score = 0
        $details = @()
        
        foreach ($fingerprintKey in $Fingerprints.Keys) {
            $fingerprint = $Fingerprints[$fingerprintKey]
            
            # 检查响应字段匹配
            if ($fingerprint.json_fields) {
                $fieldMatches = $fingerprint.json_fields | Where-Object { $signature.response_fields -contains $_ }
                if ($fieldMatches) {
                    $score += $fieldMatches.Count * 10
                    $details += "响应字段匹配: $($fieldMatches -join ', ')"
                }
            }
            
            # 检查错误码匹配
            if ($fingerprint.error_code) {
                $errorCode = $fingerprint.error_code.ToString()
                if ($signature.error_codes -contains $errorCode) {
                    $score += 20
                    $details += "错误码匹配: $errorCode"
                }
            }
            
            # 检查错误信息匹配
            if ($fingerprint.error_message) {
                $errorMsg = $fingerprint.error_message
                foreach ($code in $signature.typical_errors.Keys) {
                    $msg = $signature.typical_errors[$code]
                    if ($errorMsg -like "*$msg*" -or $msg -like "*$errorMsg*") {
                        $score += 15
                        $details += "错误信息匹配: $msg"
                    }
                }
            }
        }
        
        $matches[$vendorName] = @{
            score = $score
            details = $details
            confidence = [Math]::Min($score / 100, 1.0)
        }
    }
    
    # 按分数排序
    $sortedMatches = $matches.GetEnumerator() | Sort-Object { $_.Value.score } -Descending
    
    return $sortedMatches
}

function Generate-Report {
    Write-Log "📊 生成指纹分析报告..."
    
    $vendorMatches = Analyze-VendorMatch
    
    $report = @{
        analysis_time = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
        target_platform = $BaseUrl
        fingerprints_collected = $Fingerprints.Count
        fingerprint_data = $Fingerprints
        vendor_analysis = $vendorMatches
        top_match = if ($vendorMatches) { $vendorMatches[0] } else { $null }
    }
    
    return $report
}

# 主执行流程
Write-Host "=" * 60 -ForegroundColor Yellow
Write-Host "🕵️ API指纹识别工具 - 确定真实短信服务商" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Yellow

# 收集指纹
Test-LoginFingerprint
Test-SMSFingerprint  
Test-ErrorFingerprints

# 生成报告
$report = Generate-Report

# 输出结果
Write-Host ""
Write-Host "=" * 60 -ForegroundColor Green
Write-Host "📊 指纹分析报告" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green

Write-Host "收集到 $($report.fingerprints_collected) 个API指纹" -ForegroundColor White

if ($report.top_match) {
    $topVendor = $report.top_match.Name
    $confidence = $report.top_match.Value.confidence
    Write-Host ""
    Write-Host "🎯 最匹配的服务商: $topVendor" -ForegroundColor Green
    Write-Host "置信度: $($confidence.ToString("P"))" -ForegroundColor Green
    Write-Host "匹配详情:" -ForegroundColor Yellow
    foreach ($detail in $report.top_match.Value.details) {
        Write-Host "  • $detail" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "📊 所有服务商匹配度:" -ForegroundColor Yellow
foreach ($match in $report.vendor_analysis) {
    $vendor = $match.Name
    $score = $match.Value.score
    $confidence = $match.Value.confidence
    Write-Host "  $vendor`: 分数=$score, 置信度=$($confidence.ToString("P"))" -ForegroundColor White
}

# 保存详细报告
$reportJson = $report | ConvertTo-Json -Depth 10
$reportJson | Out-File -FilePath "api_fingerprint_report.json" -Encoding UTF8

Write-Host ""
Write-Host "📄 详细报告已保存到: api_fingerprint_report.json" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Yellow
