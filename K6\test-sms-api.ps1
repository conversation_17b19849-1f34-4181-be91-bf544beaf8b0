# 短信API测试脚本
# 用于测试 http://**************:8394 的API接口

param(
    [string]$BaseUrl = "http://**************:8394",
    [string]$Phone = "13800138000",
    [string]$Message = "测试消息",
    [string]$Sign = "测试签名"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    短信API自动测试工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 测试的API端点列表
$endpoints = @(
    "/api/sms/send",
    "/sms/send", 
    "/send",
    "/api/send",
    "/sendSms",
    "/api/sendSms",
    "/sms/api/send",
    "/message/send",
    "/api/message/send"
)

# 测试不同的请求方法
$methods = @("GET", "POST")

# 测试不同的内容类型
$contentTypes = @(
    "application/json",
    "application/x-www-form-urlencoded",
    "text/plain"
)

function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$Method,
        [string]$ContentType,
        [hashtable]$Data
    )
    
    try {
        Write-Host "测试: $Method $Url" -ForegroundColor Yellow
        
        $headers = @{
            "Content-Type" = $ContentType
            "User-Agent" = "SMS-API-Tester/1.0"
        }
        
        if ($Method -eq "GET") {
            $response = Invoke-WebRequest -Uri $Url -Method $Method -Headers $headers -TimeoutSec 10
        } else {
            if ($ContentType -eq "application/json") {
                $body = $Data | ConvertTo-Json
            } else {
                $body = ($Data.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
            }
            
            $response = Invoke-WebRequest -Uri $Url -Method $Method -Headers $headers -Body $body -TimeoutSec 10
        }
        
        Write-Host "✅ 状态码: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "📄 响应长度: $($response.Content.Length) 字节" -ForegroundColor Green
        
        # 尝试解析JSON响应
        try {
            $jsonResponse = $response.Content | ConvertFrom-Json
            Write-Host "📊 JSON响应:" -ForegroundColor Green
            Write-Host ($jsonResponse | ConvertTo-Json -Depth 3) -ForegroundColor White
        } catch {
            Write-Host "📄 文本响应 (前200字符):" -ForegroundColor Green
            $preview = $response.Content.Substring(0, [Math]::Min(200, $response.Content.Length))
            Write-Host $preview -ForegroundColor White
        }
        
        return $true
        
    } catch {
        $errorMsg = $_.Exception.Message
        if ($errorMsg -like "*404*") {
            Write-Host "❌ 404 - 端点不存在" -ForegroundColor Red
        } elseif ($errorMsg -like "*403*") {
            Write-Host "❌ 403 - 访问被拒绝" -ForegroundColor Red
        } elseif ($errorMsg -like "*500*") {
            Write-Host "❌ 500 - 服务器错误" -ForegroundColor Red
        } elseif ($errorMsg -like "*timeout*") {
            Write-Host "❌ 请求超时" -ForegroundColor Red
        } else {
            Write-Host "❌ 错误: $errorMsg" -ForegroundColor Red
        }
        return $false
    }
}

# 准备测试数据
$testData = @{
    "phone" = $Phone
    "message" = $Message
    "sign" = $Sign
    "api_key" = "test_key"
    "timestamp" = [DateTimeOffset]::UtcNow.ToUnixTimeSeconds()
}

Write-Host "🎯 目标服务器: $BaseUrl" -ForegroundColor Cyan
Write-Host "📱 测试手机号: $Phone" -ForegroundColor Cyan
Write-Host "💬 测试消息: $Message" -ForegroundColor Cyan
Write-Host ""

$successCount = 0
$totalTests = 0

# 首先测试基础连接
Write-Host "🔍 测试基础连接..." -ForegroundColor Magenta
try {
    $baseResponse = Invoke-WebRequest -Uri $BaseUrl -TimeoutSec 10
    Write-Host "✅ 服务器响应正常 (状态码: $($baseResponse.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ 无法连接到服务器: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请检查网络连接或服务器状态" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "🚀 开始API端点测试..." -ForegroundColor Magenta
Write-Host ""

# 测试所有端点
foreach ($endpoint in $endpoints) {
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor DarkGray
    
    foreach ($method in $methods) {
        foreach ($contentType in $contentTypes) {
            $url = $BaseUrl + $endpoint
            $totalTests++
            
            if (Test-ApiEndpoint -Url $url -Method $method -ContentType $contentType -Data $testData) {
                $successCount++
            }
            
            Write-Host ""
            Start-Sleep -Milliseconds 500  # 避免请求过于频繁
        }
    }
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    测试完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "📊 总测试数: $totalTests" -ForegroundColor White
Write-Host "✅ 成功响应: $successCount" -ForegroundColor Green
Write-Host "❌ 失败响应: $($totalTests - $successCount)" -ForegroundColor Red
Write-Host "📈 成功率: $([Math]::Round(($successCount / $totalTests) * 100, 2))%" -ForegroundColor Cyan

if ($successCount -gt 0) {
    Write-Host ""
    Write-Host "🎉 发现可用的API端点！" -ForegroundColor Green
    Write-Host "建议进一步分析成功响应的端点以确定正确的API格式" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "💡 建议:" -ForegroundColor Yellow
    Write-Host "1. 检查是否需要认证 (API Key, Token等)" -ForegroundColor White
    Write-Host "2. 尝试访问登录页面获取更多信息" -ForegroundColor White
    Write-Host "3. 使用浏览器开发者工具分析网络请求" -ForegroundColor White
    Write-Host "4. 联系API提供商获取官方文档" -ForegroundColor White
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
