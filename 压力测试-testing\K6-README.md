# 🚀 K6 Performance Testing Interface

一个基于Web的K6性能测试界面，支持SOCKS5代理配置和完整的测试管理功能。

## ✨ 主要功能

- 🌐 **Web界面配置** - 直观的测试参数设置
- 🔐 **SOCKS5代理支持** - 完整的代理配置和认证
- 📊 **实时测试监控** - 测试状态和结果查看
- 📈 **HTML报告生成** - 详细的性能分析报告
- 🎯 **双模式测试** - 浏览器模拟 + 真实K6测试

## 📁 项目结构 (已优化)

```
K6/
├── index.html              # 主界面文件 (集成所有功能)
├── examples/
│   └── proxy-test.js      # K6测试脚本 (支持代理)
├── run-proxy-test.bat     # 简化的批处理脚本
└── README.md              # 项目说明
```

## 🚀 快速开始

### 1. 打开主界面
```
双击打开: index.html
或访问: file:///E:/android+H5/K6/index.html
```

### 2. 配置测试参数
- **目标URL**: 要测试的网站地址
- **虚拟用户数**: 并发用户数量 (建议1-10)
- **测试时长**: 测试持续时间 (如30s)
- **请求方法**: GET/POST/PUT/DELETE

### 3. 配置代理 (可选)
- **启用SOCKS5代理**: 勾选启用
- **代理地址**: 如 `isp.decodo.com`
- **端口**: 如 `10001`
- **用户名/密码**: 代理认证信息

### 4. 运行测试
- **浏览器测试**: 快速验证功能
- **K6真实测试**: 生成K6命令并显示运行指令

## 🔧 系统要求

- **K6**: 通过Chocolatey安装 (`choco install k6`)
- **浏览器**: 现代浏览器 (Chrome/Firefox/Edge)
- **系统**: Windows环境
- **路径**: K6安装在 `C:\ProgramData\chocolatey\bin\k6.exe`

## 💡 使用说明

### 代理配置示例
```
代理类型: SOCKS5
地址: isp.decodo.com
端口: 10001
用户名: your_username
密码: your_password
```

### K6命令示例
```bash
C:\ProgramData\chocolatey\bin\k6.exe run examples\proxy-test.js \
  -e TEST_URL=https://httpbin.org/get \
  -e VUS=5 \
  -e DURATION=30s \
  -e PROXY_HOST=isp.decodo.com \
  -e PROXY_PORT=10001 \
  -e PROXY_USERNAME=your_username \
  -e PROXY_PASSWORD=your_password
```

## 🎯 功能特点

- ✅ **配置持久化** - 代理设置自动保存
- ✅ **参数验证** - 智能的输入验证
- ✅ **命令生成** - 自动生成完整K6命令
- ✅ **报告查看** - 一键打开HTML报告
- ✅ **错误处理** - 友好的错误提示

## 📊 测试报告

运行K6测试后会生成：
- `summary.html` - 详细的HTML报告
- `summary.json` - JSON格式的测试数据

## 🔍 故障排除

1. **K6命令不存在**: 确保K6已正确安装
2. **代理连接失败**: 检查代理地址和认证信息
3. **脚本执行错误**: 确保在正确的目录运行
4. **报告无法打开**: 确保测试已完成并生成了报告文件

## 📝 更新日志

- ✅ 完整的SOCKS5代理支持
- ✅ Web界面集成所有功能
- ✅ 代理配置持久化
- ✅ 智能命令生成
- ✅ 文件结构优化

## 🔗 相关资源

- [K6官方文档](https://k6.io/docs/)
- [K6 GitHub](https://github.com/grafana/k6)
- [性能测试最佳实践](https://k6.io/docs/testing-guides/)

---

**祝您测试愉快！🎉**
