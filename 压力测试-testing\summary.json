{"setup_data": {"targetUrl": "http://youyihuas.xyz/", "method": "GET", "startTime": "2025-08-02T14:25:18.768+08:00"}, "root_group": {"name": "", "path": "", "id": "d41d8cd98f00b204e9800998ecf8427e", "groups": [], "checks": [{"name": "✅ HTTP状态码正常", "path": "::✅ HTTP状态码正常", "id": "9e3542fef63c938951dffb9a1540a3f3", "passes": 1197, "fails": 262931}, {"name": "⚡ 响应时间合理", "path": "::⚡ 响应时间合理", "id": "462889065fdc366f299e485ae317651f", "passes": 246257, "fails": 17871}, {"id": "4e0344942734d025a6975abdb1ca4eff", "passes": 1317, "fails": 262811, "name": "📦 响应体存在", "path": "::📦 响应体存在"}, {"path": "::🔗 连接建立成功", "id": "f73c0aceea33f4cb8fff4b2c25ebc861", "passes": 264128, "fails": 0, "name": "🔗 连接建立成功"}, {"name": "📡 DNS解析成功", "path": "::📡 DNS解析成功", "id": "18a0742ac0b3c22eeaee1530099431c5", "passes": 0, "fails": 264128}]}, "options": {"summaryTrendStats": ["avg", "min", "med", "max", "p(90)", "p(95)", "p(99)"], "summaryTimeUnit": "", "noColor": false}, "state": {"isStdOutTTY": true, "isStdErrTTY": true, "testRunDurationMs": 9222060.4035}, "metrics": {"http_req_blocked": {"type": "trend", "contains": "time", "values": {"min": 0, "med": 0.7931, "max": 25881.0207, "p(90)": 1.6601, "p(95)": 1.795, "p(99)": 7023.693112, "avg": 120.16248833410609}}, "custom_request_count": {"type": "counter", "contains": "default", "values": {"count": 264128, "rate": 28.64088809261723}}, "http_req_tls_handshaking": {"type": "trend", "contains": "time", "values": {"p(90)": 0, "p(95)": 0, "p(99)": 0, "avg": 0, "min": 0, "med": 0, "max": 0}}, "iteration_duration": {"type": "trend", "contains": "time", "values": {"p(95)": 6228.5682750000005, "p(99)": 22694.942035000007, "avg": 3487.1894969193236, "min": 1240.4497, "med": 2973.9603500000003, "max": 34828.3746, "p(90)": 4532.80065}}, "http_req_duration{expected_response:true}": {"values": {"avg": 686.1270629072683, "min": 228.8041, "med": 460.7593, "max": 4541.0442, "p(90)": 1483.91558, "p(95)": 1873.7865600000002, "p(99)": 3606.5318799999995}, "type": "trend", "contains": "time"}, "vus": {"type": "gauge", "contains": "default", "values": {"value": 100, "min": 0, "max": 100}}, "active_users": {"type": "gauge", "contains": "default", "values": {"value": 1, "min": -1, "max": 1}}, "iterations": {"type": "counter", "contains": "default", "values": {"count": 264066, "rate": 28.63416508308495}}, "http_req_waiting": {"contains": "time", "values": {"min": 0, "med": 638.9301, "max": 30049.5294, "p(90)": 1772.83662, "p(95)": 3620.2028600000003, "p(99)": 5003.268472, "avg": 1053.2113913966411}, "type": "trend"}, "checks": {"type": "rate", "contains": "default", "values": {"rate": 0.3883715471286649, "passes": 512899, "fails": 807741}}, "data_sent": {"contains": "data", "values": {"count": 26358342, "rate": 2858.183621308353}, "type": "counter"}, "http_req_duration": {"contains": "time", "values": {"med": 640.2826, "max": 30049.5294, "p(90)": 1774.8578400000004, "p(95)": 3620.30816, "p(99)": 5003.316331999999, "avg": 1054.333452200641, "min": 0}, "thresholds": {"p(95)<500": {"ok": false}, "p(99)<1000": {"ok": false}}, "type": "trend"}, "custom_response_time": {"type": "trend", "contains": "default", "values": {"avg": 1484.5826417494548, "min": 223, "med": 666, "max": 32172, "p(90)": 1892, "p(95)": 3818, "p(99)": 21037}, "thresholds": {"p(95)<400": {"ok": false}}}, "http_req_receiving": {"type": "trend", "contains": "time", "values": {"p(95)": 0, "p(99)": 0, "avg": 0.009670289896224947, "min": 0, "med": 0, "max": 591.7, "p(90)": 0}}, "http_req_failed": {"type": "rate", "contains": "default", "values": {"fails": 1197, "rate": 0.9954681235305476, "passes": 262932}, "thresholds": {"rate<0.1": {"ok": false}}}, "http_req_connecting": {"type": "trend", "contains": "time", "values": {"avg": 54.1583362076874, "min": 0, "med": 0.7547, "max": 15059.8193, "p(90)": 1.6265, "p(95)": 1.765, "p(99)": 7.2787}}, "http_req_sending": {"type": "trend", "contains": "time", "values": {"min": 0, "med": 0, "max": 1086.8655, "p(90)": 0.18392000000000117, "p(95)": 0.5297, "p(99)": 0.9971, "avg": 1.1123905141048307}}, "vus_max": {"values": {"value": 100, "min": 100, "max": 100}, "type": "gauge", "contains": "default"}, "data_received": {"values": {"count": 3593613, "rate": 389.67571700529476}, "type": "counter", "contains": "data"}, "custom_error_rate": {"type": "rate", "contains": "default", "values": {"passes": 264128, "fails": 0, "rate": 1}, "thresholds": {"rate<0.05": {"ok": false}}}, "http_reqs": {"type": "counter", "contains": "default", "values": {"count": 264129, "rate": 28.64099652825485}}}}