# 简化版API指纹识别工具
param(
    [string]$BaseUrl = "http://**************:8394"
)

Write-Host "=" * 60 -ForegroundColor Yellow
Write-Host "🕵️ API指纹识别工具 - 确定真实短信服务商" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Yellow

$results = @{}

function Write-Log {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor Cyan
}

function Test-Endpoint {
    param(
        [string]$Endpoint,
        [hashtable]$Data,
        [string]$Method = "POST",
        [string]$ContentType = "application/json"
    )
    
    try {
        if ($Method -eq "POST") {
            if ($ContentType -eq "application/json") {
                $body = $Data | ConvertTo-Json
            } else {
                $body = ($Data.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
            }
            
            $response = Invoke-WebRequest -Uri "$BaseUrl$Endpoint" -Method $Method -Body $body -ContentType $ContentType -TimeoutSec 10 -ErrorAction Stop
        } else {
            $response = Invoke-WebRequest -Uri "$BaseUrl$Endpoint" -Method $Method -TimeoutSec 10 -ErrorAction Stop
        }
        
        $result = @{
            success = $true
            status_code = $response.StatusCode
            headers = @{}
            content_type = ""
            content_length = $response.Content.Length
            content_sample = $response.Content.Substring(0, [Math]::Min(500, $response.Content.Length))
        }
        
        # 提取关键头部信息
        if ($response.Headers["Content-Type"]) {
            $result.content_type = $response.Headers["Content-Type"][0]
        }
        if ($response.Headers["Server"]) {
            $result.server = $response.Headers["Server"][0]
        }
        if ($response.Headers["X-Powered-By"]) {
            $result.powered_by = $response.Headers["X-Powered-By"][0]
        }
        
        # 尝试解析JSON
        try {
            $jsonResponse = $response.Content | ConvertFrom-Json
            $result.is_json = $true
            $result.json_fields = $jsonResponse.PSObject.Properties.Name -join ", "
            $result.json_data = $jsonResponse
        } catch {
            $result.is_json = $false
        }
        
        return $result
        
    } catch {
        return @{
            success = $false
            error = $_.Exception.Message
            status_code = if ($_.Exception.Response) { $_.Exception.Response.StatusCode } else { "N/A" }
        }
    }
}

# 测试1: 登录页面
Write-Log "🔍 测试登录页面..."
$results.login_page = Test-Endpoint -Endpoint "/loginBlue.html" -Method "GET"

# 测试2: 登录接口
Write-Log "🔍 测试登录接口..."
$loginData = @{
    account = "test"
    password = "test"
    code = "1234"
    remembermeclient = "on"
}
$results.login_api = Test-Endpoint -Endpoint "/login" -Data $loginData -ContentType "application/x-www-form-urlencoded"

# 测试3: 验证码接口
Write-Log "🔍 测试验证码接口..."
$results.captcha = Test-Endpoint -Endpoint "/code.aspx" -Method "GET"

# 测试4: 可能的API端点
Write-Log "🔍 测试可能的API端点..."
$apiEndpoints = @("/api/sms/send", "/api/send", "/sms/send", "/send", "/sendSms", "/api/login", "/api/user/login")

foreach ($endpoint in $apiEndpoints) {
    $testData = @{
        phone = "***********"
        content = "test"
        mobile = "***********"
        msg = "test"
    }
    
    $key = "api_" + $endpoint.Replace("/", "_").Replace("-", "_")
    $results[$key] = Test-Endpoint -Endpoint $endpoint -Data $testData
    Start-Sleep -Milliseconds 500  # 避免请求过快
}

# 测试5: 错误响应
Write-Log "🔍 测试错误响应..."
$errorTests = @{
    invalid_phone = @{ phone = "invalid"; content = "test" }
    empty_content = @{ phone = "***********"; content = "" }
    no_params = @{}
}

foreach ($testName in $errorTests.Keys) {
    $key = "error_$testName"
    $results[$key] = Test-Endpoint -Endpoint "/api/sms/send" -Data $errorTests[$testName]
    Start-Sleep -Milliseconds 500
}

# 分析结果
Write-Host ""
Write-Host "=" * 60 -ForegroundColor Green
Write-Host "📊 API指纹分析结果" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green

# 服务器信息分析
Write-Host ""
Write-Host "🌐 服务器信息:" -ForegroundColor Yellow
foreach ($key in $results.Keys) {
    $result = $results[$key]
    if ($result.success -and $result.server) {
        Write-Host "  服务器类型: $($result.server)" -ForegroundColor White
        break
    }
}

foreach ($key in $results.Keys) {
    $result = $results[$key]
    if ($result.success -and $result.powered_by) {
        Write-Host "  技术栈: $($result.powered_by)" -ForegroundColor White
        break
    }
}

# 成功的端点
Write-Host ""
Write-Host "✅ 成功响应的端点:" -ForegroundColor Yellow
foreach ($key in $results.Keys) {
    $result = $results[$key]
    if ($result.success -and $result.status_code -eq 200) {
        Write-Host "  $key`: HTTP $($result.status_code) - $($result.content_type)" -ForegroundColor Green
        if ($result.is_json -and $result.json_fields) {
            Write-Host "    JSON字段: $($result.json_fields)" -ForegroundColor White
        }
    }
}

# 错误响应分析
Write-Host ""
Write-Host "❌ 错误响应分析:" -ForegroundColor Yellow
foreach ($key in $results.Keys) {
    $result = $results[$key]
    if ($result.success -and $result.status_code -ne 200) {
        Write-Host "  $key`: HTTP $($result.status_code)" -ForegroundColor Red
        if ($result.is_json -and $result.json_data) {
            Write-Host "    响应: $($result.json_data | ConvertTo-Json -Compress)" -ForegroundColor White
        }
    } elseif (-not $result.success) {
        Write-Host "  $key`: 连接失败 - $($result.error)" -ForegroundColor Red
    }
}

# JSON响应特征分析
Write-Host ""
Write-Host "📋 JSON响应特征:" -ForegroundColor Yellow
$jsonFields = @()
foreach ($key in $results.Keys) {
    $result = $results[$key]
    if ($result.success -and $result.is_json -and $result.json_fields) {
        $fields = $result.json_fields -split ", "
        $jsonFields += $fields
    }
}

if ($jsonFields) {
    $uniqueFields = $jsonFields | Sort-Object | Get-Unique
    Write-Host "  发现的字段: $($uniqueFields -join ', ')" -ForegroundColor White
    
    # 服务商特征匹配
    Write-Host ""
    Write-Host "🎯 服务商特征匹配:" -ForegroundColor Yellow
    
    $vendorMatches = @{
        "云片网络" = @("code", "msg", "data")
        "创蓝253" = @("code", "message", "msgId")
        "容联云通讯" = @("statusCode", "statusMsg", "templateSMS")
        "SUBMAIL" = @("status", "send_id", "fee")
        "亿美软通" = @("result", "msgid", "custid")
    }
    
    foreach ($vendor in $vendorMatches.Keys) {
        $vendorFields = $vendorMatches[$vendor]
        $matchCount = ($uniqueFields | Where-Object { $vendorFields -contains $_ }).Count
        $matchPercent = if ($vendorFields.Count -gt 0) { ($matchCount / $vendorFields.Count) * 100 } else { 0 }
        
        if ($matchCount -gt 0) {
            Write-Host "  $vendor`: $matchCount/$($vendorFields.Count) 字段匹配 ($([math]::Round($matchPercent))%)" -ForegroundColor Green
        } else {
            Write-Host "  $vendor`: 无匹配字段" -ForegroundColor Gray
        }
    }
}

# 保存详细结果
$reportPath = "api_fingerprint_report.json"
$results | ConvertTo-Json -Depth 5 | Out-File -FilePath $reportPath -Encoding UTF8

Write-Host ""
Write-Host "📄 详细报告已保存到: $reportPath" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Yellow

# 显示关键发现
Write-Host ""
Write-Host "🔍 关键发现总结:" -ForegroundColor Yellow

$keyFindings = @()

# 检查是否有成功的API响应
$successfulAPIs = $results.Keys | Where-Object { $results[$_].success -and $results[$_].status_code -eq 200 -and $_ -like "api_*" }
if ($successfulAPIs) {
    $keyFindings += "发现 $($successfulAPIs.Count) 个可用的API端点"
}

# 检查JSON响应
$jsonResponses = $results.Keys | Where-Object { $results[$_].is_json }
if ($jsonResponses) {
    $keyFindings += "发现 $($jsonResponses.Count) 个JSON格式响应"
}

# 检查服务器信息
$serverInfo = $results.Values | Where-Object { $_.server -or $_.powered_by } | Select-Object -First 1
if ($serverInfo) {
    if ($serverInfo.server) { $keyFindings += "服务器: $($serverInfo.server)" }
    if ($serverInfo.powered_by) { $keyFindings += "技术栈: $($serverInfo.powered_by)" }
}

foreach ($finding in $keyFindings) {
    Write-Host "  • $finding" -ForegroundColor White
}

Write-Host ""
Write-Host "🎯 建议下一步:" -ForegroundColor Yellow
Write-Host "  1. 申请测试账号进行实际API调用" -ForegroundColor White
Write-Host "  2. 分析成功响应的JSON结构确定服务商" -ForegroundColor White
Write-Host "  3. 通过错误码和错误信息进一步确认" -ForegroundColor White
Write-Host "  4. 发送实际短信测试并分析短信特征" -ForegroundColor White
