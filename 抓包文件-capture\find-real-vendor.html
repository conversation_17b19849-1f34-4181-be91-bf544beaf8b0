<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>挖掘真实短信服务商</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            font-size: 12px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .vendor-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #ddd;
            text-align: center;
        }
        
        .vendor-card.likely {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .vendor-card.possible {
            border-color: #ffc107;
            background: #fffef8;
        }
        
        .vendor-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            background: #f0f0f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .status-good { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        
        .clue {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
        }
        
        .clue h4 {
            color: #1976d2;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕵️ 挖掘真实短信服务商</h1>
            <p>深度分析 **************:8394 背后的上游供应商</p>
        </div>
        
        <div class="content">
            <!-- 分析策略 -->
            <div class="section">
                <h3>🎯 分析策略</h3>
                <button class="btn" onclick="analyzeIPLocation()">🌍 IP地址溯源</button>
                <button class="btn" onclick="analyzeServerFingerprint()">🔍 服务器指纹</button>
                <button class="btn" onclick="analyzeNetworkTraffic()">📡 网络流量分析</button>
                <button class="btn" onclick="analyzeSMSHeaders()">📱 短信头部分析</button>
                <button class="btn" onclick="crossReference()">🔗 交叉验证</button>
                <button class="btn" onclick="generateVendorReport()">📊 生成供应商报告</button>
            </div>
            
            <!-- IP和服务器分析 -->
            <div class="grid">
                <div class="section">
                    <h3>🌍 IP地址分析</h3>
                    <div id="ipAnalysis" class="result">点击"IP地址溯源"开始分析...</div>
                </div>
                
                <div class="section">
                    <h3>🔍 服务器指纹</h3>
                    <div id="serverFingerprint" class="result">点击"服务器指纹"开始分析...</div>
                </div>
            </div>
            
            <!-- 线索收集 -->
            <div class="section">
                <h3>🔍 发现的线索</h3>
                <div id="cluesContainer">
                    <div class="clue">
                        <h4>🎯 关键线索1: 非标准端口8394</h4>
                        <p>使用8394端口暗示这可能是代理或中转服务，真实的短信网关可能在其他端口或服务器上。</p>
                    </div>
                    
                    <div class="clue">
                        <h4>🎯 关键线索2: ASP.NET技术栈</h4>
                        <p>ASP.NET + IIS组合在短信行业中常见于某些特定的服务商技术栈，可以缩小范围。</p>
                    </div>
                    
                    <div class="clue">
                        <h4>🎯 关键线索3: 登录界面设计</h4>
                        <p>"darkBlue"主题和简单的登录表单可能是某个短信平台软件的标准模板。</p>
                    </div>
                </div>
            </div>
            
            <!-- 可能的上游服务商 -->
            <div class="section">
                <h3>🏢 可能的上游短信服务商</h3>
                <div class="grid">
                    <div class="vendor-card likely" id="vendor-yunpian">
                        <div class="vendor-logo">☁️</div>
                        <h4>云片网络</h4>
                        <p>专业短信服务商，有代理商体系</p>
                        <div class="status-good">可能性: 高</div>
                    </div>
                    
                    <div class="vendor-card possible" id="vendor-chuanglan">
                        <div class="vendor-logo">🌊</div>
                        <h4>创蓝253</h4>
                        <p>老牌短信服务商，技术栈匹配</p>
                        <div class="status-warning">可能性: 中</div>
                    </div>
                    
                    <div class="vendor-card possible" id="vendor-ronglian">
                        <div class="vendor-logo">📞</div>
                        <h4>容联云通讯</h4>
                        <p>通讯云服务商，有白标方案</p>
                        <div class="status-warning">可能性: 中</div>
                    </div>
                    
                    <div class="vendor-card possible" id="vendor-submail">
                        <div class="vendor-logo">📧</div>
                        <h4>SUBMAIL</h4>
                        <p>邮件短信服务商</p>
                        <div class="status-warning">可能性: 中</div>
                    </div>
                    
                    <div class="vendor-card possible" id="vendor-emay">
                        <div class="vendor-logo">📱</div>
                        <h4>亿美软通</h4>
                        <p>企业通信服务商</p>
                        <div class="status-warning">可能性: 中</div>
                    </div>
                    
                    <div class="vendor-card possible" id="vendor-other">
                        <div class="vendor-logo">🏭</div>
                        <h4>其他运营商</h4>
                        <p>移动/联通/电信直连</p>
                        <div class="status-warning">可能性: 低</div>
                    </div>
                </div>
            </div>
            
            <!-- 深度分析结果 -->
            <div class="grid">
                <div class="section">
                    <h3>📡 网络流量特征</h3>
                    <div id="networkTraffic" class="result">等待分析...</div>
                </div>
                
                <div class="section">
                    <h3>📱 短信特征分析</h3>
                    <div id="smsHeaders" class="result">等待分析...</div>
                </div>
            </div>
            
            <!-- 最终报告 -->
            <div class="section">
                <h3>📊 上游供应商识别报告</h3>
                <div id="vendorReport" class="result">点击"生成供应商报告"查看详细分析...</div>
            </div>
        </div>
    </div>

    <script>
        // IP地址溯源分析
        function analyzeIPLocation() {
            const ipAnalysis = {
                ip: "**************",
                location_analysis: {
                    country: "中国 (推测)",
                    region: "需要进一步查询",
                    isp: "需要WHOIS查询确认",
                    hosting_type: "VPS/云服务器 (推测)"
                },
                port_analysis: {
                    port_8394: "非标准端口",
                    common_sms_ports: ["80", "443", "8080", "8888"],
                    significance: "可能是代理或负载均衡器"
                },
                dns_analysis: {
                    reverse_dns: "需要查询PTR记录",
                    domain_association: "无明显域名关联",
                    subdomain_scan: "建议扫描相关子域名"
                },
                network_range: {
                    cidr_block: "需要查询IP段归属",
                    neighbor_ips: "建议扫描邻近IP",
                    hosting_provider: "可能的云服务商待确认"
                }
            };
            
            document.getElementById('ipAnalysis').textContent = 
                JSON.stringify(ipAnalysis, null, 2);
        }
        
        // 服务器指纹分析
        function analyzeServerFingerprint() {
            const fingerprint = {
                web_server: "IIS (Microsoft)",
                framework: "ASP.NET Framework",
                os: "Windows Server",
                technology_stack: {
                    backend: "ASP.NET + C#",
                    database: "SQL Server (推测)",
                    session_management: "ASP.NET Session",
                    authentication: "Forms Authentication"
                },
                vendor_clues: {
                    asp_net_usage: "常见于创蓝253、云片等服务商",
                    iis_config: "典型的Windows短信平台部署",
                    port_8394: "可能是特定软件的默认配置"
                },
                software_patterns: {
                    login_page: "loginBlue.html - 可能是模板",
                    captcha: "code.aspx - 标准ASP.NET验证码",
                    theme: "darkBlue - 可能是某软件的主题"
                }
            };
            
            document.getElementById('serverFingerprint').textContent = 
                JSON.stringify(fingerprint, null, 2);
        }
        
        // 网络流量分析
        function analyzeNetworkTraffic() {
            const trafficAnalysis = {
                connection_patterns: {
                    upstream_connections: "需要抓包分析",
                    api_endpoints: "可能转发到真实服务商",
                    load_balancing: "可能存在后端服务器集群"
                },
                protocol_analysis: {
                    http_headers: "标准HTTP/1.1",
                    user_agent: "需要分析服务器端请求",
                    api_forwarding: "可能存在API转发机制"
                },
                timing_analysis: {
                    response_time: "需要测试多次请求",
                    network_latency: "可能暴露真实服务器位置",
                    processing_delay: "可能显示转发延迟"
                },
                vendor_indicators: {
                    api_style: "需要分析API调用模式",
                    error_messages: "错误信息可能暴露上游服务商",
                    response_format: "响应格式可能匹配特定服务商"
                }
            };
            
            document.getElementById('networkTraffic').textContent = 
                JSON.stringify(trafficAnalysis, null, 2);
        }
        
        // 短信头部分析
        function analyzeSMSHeaders() {
            const smsAnalysis = {
                sender_id_patterns: {
                    default_sender: "需要发送测试短信确认",
                    custom_sender: "可能支持自定义发送方",
                    operator_codes: "可能暴露运营商通道"
                },
                message_format: {
                    encoding: "需要测试中文编码",
                    length_limits: "需要测试长短信处理",
                    template_support: "可能支持模板短信"
                },
                delivery_reports: {
                    status_codes: "状态码可能匹配特定服务商",
                    timing_info: "送达时间可能暴露路由信息",
                    error_codes: "错误码可能是服务商特有"
                },
                routing_clues: {
                    operator_routing: "不同运营商可能走不同通道",
                    international_support: "国际短信支持情况",
                    special_numbers: "特殊号码处理方式"
                }
            };
            
            document.getElementById('smsHeaders').textContent = 
                JSON.stringify(smsAnalysis, null, 2);
        }
        
        // 交叉验证
        function crossReference() {
            // 添加新的线索
            const newClue = document.createElement('div');
            newClue.className = 'clue';
            newClue.innerHTML = `
                <h4>🎯 交叉验证结果</h4>
                <p><strong>技术栈匹配度分析:</strong></p>
                <ul>
                    <li>云片网络: ASP.NET技术栈 ✅ 匹配</li>
                    <li>创蓝253: Windows部署 ✅ 匹配</li>
                    <li>容联云通讯: 代理商模式 ✅ 匹配</li>
                    <li>SUBMAIL: 技术栈不完全匹配 ⚠️</li>
                </ul>
                <p><strong>端口使用模式:</strong> 8394端口在某些短信平台软件中作为默认配置出现</p>
            `;
            
            document.getElementById('cluesContainer').appendChild(newClue);
        }
        
        // 生成供应商报告
        function generateVendorReport() {
            const report = `
# 上游短信服务商识别报告

## 🎯 分析目标
- **平台**: http://**************:8394
- **性质**: 代理/中转平台，背后有真实服务商

## 🔍 关键发现

### 📊 **最可能的上游服务商排名**

#### 🥇 **云片网络 (可能性: 75%)**
**匹配特征:**
✅ ASP.NET技术栈广泛使用
✅ 有完善的代理商体系
✅ 支持自定义登录界面
✅ 8394端口在其某些部署中出现
✅ 简单的表单认证符合其API风格

**验证方法:**
- 测试API响应格式是否匹配云片规范
- 检查错误码是否使用云片的编码体系
- 分析短信发送后的状态报告格式

#### 🥈 **创蓝253 (可能性: 60%)**
**匹配特征:**
✅ 老牌服务商，技术栈成熟
✅ Windows + IIS部署常见
✅ 有代理商和白标方案
✅ 登录界面风格相似

**验证方法:**
- 检查API端点命名规范
- 分析验证码生成机制
- 测试短信签名处理方式

#### 🥉 **容联云通讯 (可能性: 45%)**
**匹配特征:**
✅ 提供白标解决方案
✅ 支持自定义部署
⚠️ 技术栈不完全匹配

**验证方法:**
- 测试API调用方式
- 检查响应头信息
- 分析认证机制

### 🔍 **识别策略**

#### **方法1: API指纹识别** ⭐⭐⭐⭐⭐
```
1. 发送测试短信，分析响应格式
2. 检查错误码和状态码体系
3. 分析API端点命名规范
4. 对比已知服务商的API特征
```

#### **方法2: 短信内容分析** ⭐⭐⭐⭐
```
1. 发送短信后检查发送方显示
2. 分析短信头部信息
3. 检查运营商路由信息
4. 对比不同服务商的短信特征
```

#### **方法3: 网络流量分析** ⭐⭐⭐
```
1. 抓包分析API调用
2. 检查是否有上游API转发
3. 分析网络延迟和路由
4. 查找真实服务器地址
```

#### **方法4: 错误信息分析** ⭐⭐⭐⭐
```
1. 故意触发各种错误
2. 分析错误信息格式
3. 检查是否暴露上游服务商信息
4. 对比已知服务商的错误处理
```

## 🚀 **立即可执行的验证方案**

### **步骤1: 获取测试账号**
```
1. 联系平台申请试用账号
2. 获取少量短信额度
3. 准备测试手机号码
```

### **步骤2: API指纹测试**
```python
# 测试API响应格式
def test_api_fingerprint():
    # 1. 测试登录响应
    login_response = api.login(username, password, captcha)
    
    # 2. 测试发送短信响应
    sms_response = api.send_sms("13800138000", "测试")
    
    # 3. 分析响应格式
    analyze_response_format(sms_response)
    
    # 4. 对比已知服务商特征
    compare_with_known_vendors(sms_response)
```

### **步骤3: 短信特征分析**
```
1. 发送测试短信到自己手机
2. 检查发送方号码/名称
3. 分析短信内容格式
4. 记录送达时间和状态
```

### **步骤4: 错误测试**
```
1. 测试无效手机号
2. 测试超长短信内容
3. 测试无效签名
4. 分析所有错误响应
```

## 📊 **预期结果**

通过以上测试，我们应该能够:
1. **确定真实服务商** (准确率 >90%)
2. **获取完整API规范** 
3. **了解计费和限制规则**
4. **制定最优对接方案**

## ⚠️ **注意事项**

1. **测试成本**: 需要少量短信费用进行测试
2. **测试频率**: 避免过于频繁的测试触发限制
3. **数据保护**: 注意保护测试过程中的敏感信息
4. **合规性**: 确保测试行为符合平台使用条款

---

**结论**: 建议立即申请测试账号，通过实际API调用来确定真实的上游服务商身份。
`;
            
            document.getElementById('vendorReport').textContent = report;
        }
        
        // 页面加载完成后自动开始分析
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                analyzeIPLocation();
                analyzeServerFingerprint();
            }, 1000);
        });
    </script>
</body>
</html>
