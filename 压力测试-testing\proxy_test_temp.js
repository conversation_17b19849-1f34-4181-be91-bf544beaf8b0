import http from 'k6/http'; 
import { check } from 'k6'; 
 
export let options = { 
  vus: 1, 
  duration: '10s', 
}; 
 
export default function() { 
  const proxyUrl = 'socks5://spo5nyyeb6:j6IJjtrro64Fie=<EMAIL>:10001'; 
  console.log('Testing proxy connection...'); 
 
  const params = { 
    proxy: proxyUrl, 
    timeout: '30s' 
  }; 
 
  console.log('Testing basic connectivity...'); 
  const response1 = http.get('https://httpbin.org/ip', params); 
  const proxyWorking = check(response1, { 
    'Proxy connection successful': (r) => r.status === 200, 
    'Response time reasonable': (r) => r.timings.duration < 10000, 
  }); 
 
  if (proxyWorking) { 
    console.log('SUCCESS: Proxy is working!'); 
    console.log('Response time: ' + response1.timings.duration + 'ms'); 
  } else { 
    console.log('FAILED: Proxy connection failed!'); 
    console.log('Status: ' + response1.status); 
  } 
} 
