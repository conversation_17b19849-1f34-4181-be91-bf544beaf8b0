#!/bin/bash

# API压力测试监控脚本
# 使用方法: ./monitor-api.sh [API_URL] [监控时长秒数]

API_URL=${1:-"http://localhost:8080"}
DURATION=${2:-300}  # 默认监控5分钟
LOG_FILE="api_monitor_$(date +%Y%m%d_%H%M%S).log"

echo "🚀 开始监控API: $API_URL"
echo "⏱️ 监控时长: ${DURATION}秒"
echo "📝 日志文件: $LOG_FILE"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 创建日志文件
echo "时间,CPU使用率,内存使用率,网络连接数,响应时间ms,HTTP状态码" > $LOG_FILE

# 监控函数
monitor_api() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 获取系统资源
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    local memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    local connections=$(netstat -an | grep :80 | wc -l)
    
    # 测试API响应
    local start_time=$(date +%s%3N)
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 $API_URL)
    local end_time=$(date +%s%3N)
    local response_time=$((end_time - start_time))
    
    # 输出到控制台
    printf "⏰ %s | 🖥️ CPU: %s%% | 💾 内存: %s%% | 🌐 连接: %s | ⚡ 响应: %sms | 📊 状态: %s\n" \
           "$timestamp" "$cpu_usage" "$memory_usage" "$connections" "$response_time" "$http_code"
    
    # 写入日志文件
    echo "$timestamp,$cpu_usage,$memory_usage,$connections,$response_time,$http_code" >> $LOG_FILE
    
    # 检查异常情况
    if [ "$http_code" != "200" ]; then
        echo "❌ 警告: HTTP状态码异常 - $http_code"
    fi
    
    if [ "$response_time" -gt 5000 ]; then
        echo "⚠️ 警告: 响应时间过长 - ${response_time}ms"
    fi
    
    if (( $(echo "$cpu_usage > 80" | bc -l) )); then
        echo "🔥 警告: CPU使用率过高 - ${cpu_usage}%"
    fi
}

# 开始监控循环
start_time=$(date +%s)
while true; do
    current_time=$(date +%s)
    elapsed=$((current_time - start_time))
    
    if [ $elapsed -ge $DURATION ]; then
        break
    fi
    
    monitor_api
    sleep 2
done

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "✅ 监控完成！"
echo "📊 监控数据已保存到: $LOG_FILE"
echo "📈 可以使用Excel或其他工具分析数据"

# 生成简单统计
echo ""
echo "📋 监控摘要:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 统计HTTP状态码
echo "📊 HTTP状态码分布:"
tail -n +2 $LOG_FILE | cut -d',' -f6 | sort | uniq -c | while read count code; do
    echo "   $code: $count 次"
done

# 计算平均响应时间
avg_response=$(tail -n +2 $LOG_FILE | cut -d',' -f5 | awk '{sum+=$1; count++} END {printf "%.1f", sum/count}')
echo "⚡ 平均响应时间: ${avg_response}ms"

# 计算最大响应时间
max_response=$(tail -n +2 $LOG_FILE | cut -d',' -f5 | sort -n | tail -1)
echo "🔥 最大响应时间: ${max_response}ms"

# 统计错误数量
error_count=$(tail -n +2 $LOG_FILE | cut -d',' -f6 | grep -v "200" | wc -l)
total_count=$(tail -n +2 $LOG_FILE | wc -l)
if [ $total_count -gt 0 ]; then
    error_rate=$(echo "scale=2; $error_count * 100 / $total_count" | bc)
    echo "❌ 错误率: ${error_rate}%"
fi

echo ""
echo "💡 建议: 使用以下命令查看详细数据:"
echo "   cat $LOG_FILE"
echo "   或导入Excel进行图表分析"
