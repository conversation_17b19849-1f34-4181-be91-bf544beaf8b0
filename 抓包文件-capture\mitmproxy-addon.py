"""
mitmproxy增强插件 - 提供完整的Web可视化功能
安装位置: E:\mitmproxy-setup\scripts\addon.py
"""

import json
import time
from datetime import datetime
from mitmproxy import http, ctx
import os

class MitmproxyEnhancer:
    def __init__(self):
        self.requests_log = []
        self.log_file = "E:/mitmproxy-setup/logs/requests.json"
        self.ensure_log_dir()
    
    def ensure_log_dir(self):
        """确保日志目录存在"""
        os.makedirs("E:/mitmproxy-setup/logs", exist_ok=True)
    
    def request(self, flow: http.HTTPFlow) -> None:
        """处理请求"""
        request_data = {
            "timestamp": datetime.now().isoformat(),
            "method": flow.request.method,
            "url": flow.request.pretty_url,
            "host": flow.request.pretty_host,
            "path": flow.request.path,
            "headers": dict(flow.request.headers),
            "content": self.get_content(flow.request),
            "size": len(flow.request.content) if flow.request.content else 0
        }
        
        # 添加到内存日志
        self.requests_log.append(request_data)
        
        # 保持最近1000条记录
        if len(self.requests_log) > 1000:
            self.requests_log = self.requests_log[-1000:]
        
        # 实时保存到文件
        self.save_to_file(request_data, "request")
        
        # 控制台输出
        ctx.log.info(f"📱 {flow.request.method} {flow.request.pretty_url}")
    
    def response(self, flow: http.HTTPFlow) -> None:
        """处理响应"""
        if flow.response:
            response_data = {
                "timestamp": datetime.now().isoformat(),
                "url": flow.request.pretty_url,
                "status_code": flow.response.status_code,
                "headers": dict(flow.response.headers),
                "content": self.get_content(flow.response),
                "size": len(flow.response.content) if flow.response.content else 0,
                "duration": flow.response.timestamp_end - flow.request.timestamp_start if flow.response.timestamp_end else 0
            }
            
            # 保存响应数据
            self.save_to_file(response_data, "response")
            
            # 控制台输出
            status_emoji = "✅" if 200 <= flow.response.status_code < 300 else "❌"
            ctx.log.info(f"{status_emoji} {flow.response.status_code} {flow.request.pretty_url} ({response_data['duration']:.2f}s)")
    
    def get_content(self, message):
        """安全获取内容"""
        try:
            if message.content:
                # 尝试解码为文本
                content = message.content.decode('utf-8', errors='ignore')
                # 如果是JSON，格式化输出
                if message.headers.get('content-type', '').startswith('application/json'):
                    try:
                        return json.loads(content)
                    except:
                        return content
                return content
            return ""
        except:
            return "[二进制数据]"
    
    def save_to_file(self, data, data_type):
        """保存数据到文件"""
        try:
            log_entry = {
                "type": data_type,
                "data": data,
                "saved_at": datetime.now().isoformat()
            }
            
            # 追加到日志文件
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
        except Exception as e:
            ctx.log.error(f"保存日志失败: {e}")

# 注册插件
addons = [MitmproxyEnhancer()]
