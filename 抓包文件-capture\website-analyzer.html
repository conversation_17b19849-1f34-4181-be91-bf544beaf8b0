<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站深度分析工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .analysis-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ddd;
        }
        
        .analysis-item h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 网站深度分析工具</h1>
            <p>分析目标网站的技术架构、API端点和服务商信息</p>
        </div>
        
        <div class="content">
            <!-- 目标网站配置 -->
            <div class="section">
                <h3>🎯 目标网站</h3>
                <div class="form-group">
                    <label for="targetUrl">网站URL:</label>
                    <input type="text" id="targetUrl" value="http://175.27.145.189:8394/loginBlue.html" placeholder="输入要分析的网站URL">
                </div>
                <button class="btn" onclick="analyzeWebsite()">🚀 开始分析</button>
                <button class="btn" onclick="loadInFrame()">📱 在框架中加载</button>
                <button class="btn" onclick="findApiDocs()">📚 查找API文档</button>
            </div>
            
            <!-- 网站预览 -->
            <div class="section">
                <h3>📱 网站预览</h3>
                <iframe id="websiteFrame" src="about:blank"></iframe>
            </div>
            
            <!-- 分析结果 -->
            <div class="section">
                <h3>📊 分析结果</h3>
                <div class="analysis-grid" id="analysisResults">
                    <!-- 分析结果将在这里显示 -->
                </div>
            </div>
            
            <!-- 详细信息 -->
            <div class="section">
                <h3>🔍 详细信息</h3>
                <div id="detailResult" class="result">等待分析...</div>
            </div>
        </div>
    </div>

    <script>
        // 分析网站
        async function analyzeWebsite() {
            const url = document.getElementById('targetUrl').value;
            const resultsDiv = document.getElementById('analysisResults');
            const detailDiv = document.getElementById('detailResult');
            
            detailDiv.textContent = '正在分析网站...';
            resultsDiv.innerHTML = '';
            
            try {
                // 基础信息分析
                const analysis = await performAnalysis(url);
                displayAnalysisResults(analysis);
                
                // 详细信息
                detailDiv.textContent = JSON.stringify(analysis, null, 2);
                
            } catch (error) {
                detailDiv.textContent = `分析失败: ${error.message}`;
            }
        }
        
        // 执行分析
        async function performAnalysis(url) {
            const results = {
                timestamp: new Date().toISOString(),
                url: url,
                basicInfo: {},
                apiEndpoints: [],
                technologies: [],
                possibleVendors: []
            };
            
            // 尝试获取基础信息
            try {
                const response = await fetch(url, { 
                    method: 'GET',
                    mode: 'no-cors' // 避免CORS问题
                });
                
                results.basicInfo = {
                    accessible: true,
                    cors: 'blocked'
                };
            } catch (error) {
                results.basicInfo = {
                    accessible: false,
                    error: error.message
                };
            }
            
            // 分析URL结构
            const urlObj = new URL(url);
            results.basicInfo.domain = urlObj.hostname;
            results.basicInfo.port = urlObj.port || '80';
            results.basicInfo.path = urlObj.pathname;
            
            // 推测可能的API端点
            const commonEndpoints = [
                '/api/sms/send',
                '/api/send',
                '/sms/send',
                '/send',
                '/api/sendSms',
                '/sendSms',
                '/api/message/send',
                '/message/send',
                '/api/v1/sms/send',
                '/api/v2/sms/send',
                '/sms/api/send',
                '/api/sms',
                '/sms',
                '/api',
                '/doc',
                '/docs',
                '/api-docs',
                '/swagger',
                '/help',
                '/manual'
            ];
            
            results.apiEndpoints = commonEndpoints.map(endpoint => ({
                endpoint: endpoint,
                fullUrl: `${urlObj.protocol}//${urlObj.host}${endpoint}`,
                tested: false,
                status: 'unknown'
            }));
            
            // 技术栈分析
            results.technologies = [
                { name: 'Web Server', value: 'IIS (推测)', confidence: 'medium' },
                { name: 'Platform', value: 'Windows Server', confidence: 'medium' },
                { name: 'Framework', value: 'ASP.NET (推测)', confidence: 'low' },
                { name: 'Database', value: 'SQL Server (推测)', confidence: 'low' }
            ];
            
            // 可能的服务商分析
            results.possibleVendors = [
                { name: '自建平台', probability: 60, reason: '独立IP和端口配置' },
                { name: '白标方案', probability: 30, reason: '通用登录页面设计' },
                { name: '知名服务商', probability: 10, reason: '需要进一步验证' }
            ];
            
            return results;
        }
        
        // 显示分析结果
        function displayAnalysisResults(analysis) {
            const resultsDiv = document.getElementById('analysisResults');
            
            // 基础信息卡片
            const basicCard = createAnalysisCard('🌐 基础信息', [
                `域名: ${analysis.basicInfo.domain}`,
                `端口: ${analysis.basicInfo.port}`,
                `路径: ${analysis.basicInfo.path}`,
                `状态: ${analysis.basicInfo.accessible ? '✅ 可访问' : '❌ 不可访问'}`
            ]);
            
            // 技术栈卡片
            const techCard = createAnalysisCard('🔧 技术栈', 
                analysis.technologies.map(tech => 
                    `${tech.name}: ${tech.value} (${tech.confidence})`
                )
            );
            
            // 可能的服务商卡片
            const vendorCard = createAnalysisCard('🏢 可能的服务商', 
                analysis.possibleVendors.map(vendor => 
                    `${vendor.name}: ${vendor.probability}% - ${vendor.reason}`
                )
            );
            
            // API端点卡片
            const apiCard = createAnalysisCard('🔗 可能的API端点', 
                analysis.apiEndpoints.slice(0, 10).map(api => 
                    `${api.endpoint} - ${api.status}`
                )
            );
            
            resultsDiv.appendChild(basicCard);
            resultsDiv.appendChild(techCard);
            resultsDiv.appendChild(vendorCard);
            resultsDiv.appendChild(apiCard);
        }
        
        // 创建分析卡片
        function createAnalysisCard(title, items) {
            const card = document.createElement('div');
            card.className = 'analysis-item';
            
            const titleEl = document.createElement('h4');
            titleEl.textContent = title;
            card.appendChild(titleEl);
            
            items.forEach(item => {
                const itemEl = document.createElement('div');
                itemEl.textContent = item;
                itemEl.style.marginBottom = '5px';
                card.appendChild(itemEl);
            });
            
            return card;
        }
        
        // 在框架中加载网站
        function loadInFrame() {
            const url = document.getElementById('targetUrl').value;
            const frame = document.getElementById('websiteFrame');
            frame.src = url;
        }
        
        // 查找API文档
        function findApiDocs() {
            const url = document.getElementById('targetUrl').value;
            const urlObj = new URL(url);
            const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
            
            const docUrls = [
                `${baseUrl}/api`,
                `${baseUrl}/docs`,
                `${baseUrl}/api-docs`,
                `${baseUrl}/swagger`,
                `${baseUrl}/help`,
                `${baseUrl}/manual`,
                `${baseUrl}/doc`,
                `${baseUrl}/documentation`
            ];
            
            const detailDiv = document.getElementById('detailResult');
            detailDiv.textContent = '正在查找API文档...\n\n尝试的URL:\n' + docUrls.join('\n');
            
            // 在新窗口中打开这些URL进行手动检查
            docUrls.forEach((docUrl, index) => {
                setTimeout(() => {
                    window.open(docUrl, `_blank_${index}`);
                }, index * 1000); // 每秒打开一个
            });
        }
        
        // 页面加载完成后自动分析
        document.addEventListener('DOMContentLoaded', function() {
            console.log('网站分析工具已加载');
            loadInFrame(); // 自动加载目标网站
        });
    </script>
</body>
</html>
