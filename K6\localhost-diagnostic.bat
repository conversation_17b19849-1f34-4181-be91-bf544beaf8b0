@echo off
chcp 65001 >nul
echo ============================================================
echo 🔍 本地服务诊断工具 - http://127.0.0.1:8081
echo ============================================================
echo.

set TARGET_PORT=8081
set TARGET_URL=http://127.0.0.1:8081

echo [%time%] 开始诊断本地服务...
echo.

echo === 1. 检查端口占用情况 ===
echo 检查端口 %TARGET_PORT% 的占用情况:
netstat -ano | findstr :%TARGET_PORT%
if %errorlevel%==0 (
    echo ✅ 端口 %TARGET_PORT% 有进程在监听
    echo.
    echo 详细进程信息:
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :%TARGET_PORT%') do (
        echo 进程ID: %%a
        tasklist | findstr %%a
    )
) else (
    echo ❌ 端口 %TARGET_PORT% 没有进程在监听
    echo 💡 这可能是服务未启动的原因
)
echo.

echo === 2. 尝试连接测试 ===
echo 测试连接到 %TARGET_URL%...
curl -s -w "HTTP状态码: %%{http_code}\n连接时间: %%{time_connect}s\n总时间: %%{time_total}s\n" --connect-timeout 5 %TARGET_URL%
if %errorlevel%==0 (
    echo ✅ 连接成功
) else (
    echo ❌ 连接失败
)
echo.

echo === 3. 检查相关进程 ===
echo 查找可能相关的进程:
echo.
echo Java进程:
tasklist | findstr /i java
echo.
echo Node.js进程:
tasklist | findstr /i node
echo.
echo Python进程:
tasklist | findstr /i python
echo.
echo 其他Web服务进程:
tasklist | findstr /i "nginx\|apache\|tomcat\|jetty"
echo.

echo === 4. 检查防火墙状态 ===
echo 检查Windows防火墙状态:
netsh advfirewall show allprofiles state
echo.

echo === 5. 检查常见端口 ===
echo 检查其他常见的本地服务端口:
set common_ports=8080 8081 8082 8083 8084 8085 3000 3001 4200 5000 9000
for %%p in (%common_ports%) do (
    echo 检查端口 %%p:
    netstat -ano | findstr :%%p | findstr LISTENING
)
echo.

echo === 6. 尝试其他可能的地址 ===
echo 尝试访问相关的可能地址:
set test_urls=http://localhost:8081 http://127.0.0.1:8080 http://localhost:8080 http://127.0.0.1:3000 http://localhost:3000
for %%u in (%test_urls%) do (
    echo 测试: %%u
    curl -s -w "  状态码: %%{http_code}" --connect-timeout 2 %%u >nul 2>&1
    if !errorlevel!==0 (
        echo   ✅ 可访问
    ) else (
        echo   ❌ 不可访问
    )
)
echo.

echo === 7. 系统信息 ===
echo 操作系统信息:
systeminfo | findstr /C:"OS Name" /C:"OS Version"
echo.
echo 网络配置:
ipconfig | findstr /C:"IPv4" /C:"默认网关"
echo.

echo ============================================================
echo 📊 诊断总结
echo ============================================================
echo.

echo 🎯 可能的解决方案:
echo.
echo 如果端口没有进程监听:
echo   1. 启动相应的应用程序或服务
echo   2. 检查应用程序的配置文件
echo   3. 查看应用程序的启动日志
echo.
echo 如果端口有进程但无法访问:
echo   1. 检查防火墙设置
echo   2. 确认应用程序监听的是正确的地址
echo   3. 检查应用程序是否正常运行
echo.
echo 如果是开发环境:
echo   1. 重新启动开发服务器
echo   2. 检查package.json或配置文件中的端口设置
echo   3. 尝试使用不同的端口
echo.

echo 💡 常见的启动命令:
echo   Node.js: npm start 或 node server.js
echo   Python: python app.py 或 python manage.py runserver
echo   Java: java -jar app.jar 或 mvn spring-boot:run
echo   前端开发: npm run dev 或 ng serve
echo.

echo 🔍 需要更多信息?
echo   请提供以下信息以获得更精确的帮助:
echo   - 这是什么类型的应用程序?
echo   - 您是如何启动这个服务的?
echo   - 之前是否能正常访问?
echo   - 最近是否有任何更改?
echo.

pause
