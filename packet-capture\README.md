# 🔍 网络抓包与API分析工具集

本文件夹包含所有与网络抓包、API分析和逆向工程相关的工具和脚本。

## 📁 文件夹内容

### 🚀 **mitmproxy抓包工具**
- `mitmproxy-complete-setup.md` - mitmproxy完整安装和配置指南
- `mitmproxy-install.bat` - mitmproxy自动安装脚本
- `mitmproxy-oneclick.bat` - 一键启动mitmproxy Web界面
- `mitmproxy-advanced.bat` - 高级配置启动脚本
- `mitmproxy-addon.py` - mitmproxy自定义插件

### 🔧 **代理和网络工具**
- `check-proxy.bat` - 代理连接检查脚本
- `localhost-diagnostic.bat` - 本地网络诊断工具
- `monitor-api.sh` - API监控脚本

### 🔍 **API分析工具**
- `api-fingerprint-detector.ps1` - API指纹识别PowerShell脚本
- `api-fingerprint-detector.py` - API指纹识别Python脚本
- `api-fingerprint-test.bat` - API指纹测试批处理
- `api-reverse-analysis.md` - API逆向分析说明文档

### 📱 **短信API专项分析**
- `sms-api-analysis-guide.md` - 短信API分析完整指南
- `sms-api-analysis-report.md` - 短信API分析报告
- `sms-api-client.py` - 短信API客户端脚本
- `sms-api-tester.html` - 短信API测试Web界面

### 🕵️ **网站分析工具**
- `website-analyzer.html` - 网站分析器Web界面
- `reverse-engineer.html` - 逆向工程分析界面
- `page-source.html` - 页面源码分析

### 🔎 **厂商识别工具**
- `identify-vendor.html` - 厂商识别Web界面
- `find-real-vendor.html` - 真实厂商查找工具
- `find-api-docs.bat` - API文档查找脚本

### 📊 **分析报告**
- `final-analysis-report.md` - 最终分析报告

## 🚀 快速开始

### 1. 启动mitmproxy抓包
```bash
# 一键启动Web界面
双击运行: mitmproxy-oneclick.bat

# 或手动启动
mitmweb --web-host 0.0.0.0 --web-port 8081 --listen-port 8080
```

### 2. 配置设备代理
- **Android**: 设置 → WiFi → 代理 → 手动
- **iOS**: 设置 → WiFi → 配置代理 → 手动
- **代理地址**: 电脑IP地址
- **端口**: 8080

### 3. 安装证书
- 浏览器访问: `mitm.it`
- 下载并安装对应系统的证书

### 4. 开始抓包分析
- 访问 `http://127.0.0.1:8081` 查看Web界面
- 使用各种分析工具进行API逆向

## 🔧 主要功能

### 📡 **实时抓包**
- HTTP/HTTPS流量监控
- 请求/响应详细分析
- 实时过滤和搜索

### 🔍 **API分析**
- 自动API指纹识别
- 参数结构分析
- 认证机制检测

### 🕵️ **逆向工程**
- 网站结构分析
- 厂商技术栈识别
- API文档自动生成

### 📱 **移动端专项**
- Android/iOS APP抓包
- 短信API专项分析
- 移动端证书配置

## 💡 使用技巧

1. **抓包前准备**: 确保设备和电脑在同一网络
2. **证书安装**: 必须安装并信任mitmproxy证书
3. **过滤设置**: 使用过滤器聚焦目标流量
4. **数据导出**: 支持HAR、curl等多种格式导出

## 🎯 分析流程

1. **环境准备** → 安装工具和配置网络
2. **开始抓包** → 启动mitmproxy并配置设备
3. **流量分析** → 使用各种工具分析API
4. **逆向工程** → 识别技术栈和API结构
5. **生成报告** → 整理分析结果和文档

## ⚠️ 注意事项

- 仅用于合法的安全测试和学习目的
- 遵守相关法律法规和道德规范
- 不要用于未经授权的网络监听
- 保护个人隐私和敏感信息
