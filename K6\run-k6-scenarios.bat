@echo off
echo K6 Performance Testing Scenarios
echo ========================================

REM Basic configuration
set API_URL=https://ogy5odq5m.wujitianshi.com/467jwg?dc2cd93=e7983536baf071c160a4cf224574a84a
set PROXY_HOST=isp.decodo.com
set PROXY_PORT=10001
set PROXY_USER=spo5nyyeb6
set PROXY_PASS=j6IJjtrro64Fie=l2W
set K6_PATH=C:\ProgramData\chocolatey\bin\k6.exe

echo Target API: %API_URL%
echo Proxy Server: %PROXY_HOST%:%PROXY_PORT%
echo ========================================
echo.

echo Select Test Scenario:
echo.
echo 1. Simple Load Test (50 users, 60 seconds)
echo 2. Gradual Load Test (ramp up to 50 users, 8 minutes)
echo 3. Spike Test (burst to 100 users, 300 minutes)
echo 4. Endurance Test (20 users, 10 minutes)
echo 5. Light Test (5 users, 30 seconds)
echo 6. Extreme Test (1000 users, 1h)
echo 0. Exit
echo.

set /p choice=Enter your choice (0-6): 

if "%choice%"=="0" goto :end
if "%choice%"=="1" goto :simple
if "%choice%"=="2" goto :gradual
if "%choice%"=="3" goto :spike
if "%choice%"=="4" goto :endurance
if "%choice%"=="5" goto :light
if "%choice%"=="6" goto :extreme

echo Invalid choice, please try again
pause
goto :end

:simple
echo.
echo Running Simple Load Test...
echo Config: 50 users, 60 seconds
%K6_PATH% run examples\proxy-test.js ^
  --out json=simple_test.json ^
  --summary-export=simple_summary.json ^
  -e TEST_URL=%API_URL% ^
  -e VUS=50 ^
  -e DURATION=60s ^
  -e SCENARIO=simple ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USERNAME=%PROXY_USER% ^
  -e PROXY_PASSWORD=%PROXY_PASS%
goto :finish

:gradual
echo.
echo Running Gradual Load Test...
echo Config: Ramp up to 50 users, 8 minutes
%K6_PATH% run examples\proxy-test.js ^
  --out json=gradual_test.json ^
  --summary-export=gradual_summary.json ^
  -e TEST_URL=%API_URL% ^
  -e SCENARIO=gradual ^
  -e MAX_VUS=50 ^
  -e DURATION=8m ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USERNAME=%PROXY_USER% ^
  -e PROXY_PASSWORD=%PROXY_PASS%
goto :finish

:spike
echo.
echo Running Spike Test...
echo Config: Base 10 users, spike to 100 users, 3 minutes
%K6_PATH% run examples\proxy-test.js ^
  --out json=spike_test.json ^
  --summary-export=spike_summary.json ^
  -e TEST_URL=%API_URL% ^
  -e SCENARIO=spike ^
  -e VUS=10 ^
  -e SPIKE_VUS=100 ^
  -e DURATION=3m ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USERNAME=%PROXY_USER% ^
  -e PROXY_PASSWORD=%PROXY_PASS%
goto :finish

:endurance
echo.
echo Running Endurance Test...
echo Config: 20 users, 10 minutes sustained
%K6_PATH% run examples\proxy-test.js ^
  --out json=endurance_test.json ^
  --summary-export=endurance_summary.json ^
  -e TEST_URL=%API_URL% ^
  -e SCENARIO=endurance ^
  -e VUS=20 ^
  -e DURATION=10m ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USERNAME=%PROXY_USER% ^
  -e PROXY_PASSWORD=%PROXY_PASS%
goto :finish

:light
echo.
echo Running Light Test...
echo Config: 5 users, 30 seconds
%K6_PATH% run examples\proxy-test.js ^
  --out json=light_test.json ^
  --summary-export=light_summary.json ^
  -e TEST_URL=%API_URL% ^
  -e VUS=5 ^
  -e DURATION=30s ^
  -e SCENARIO=simple ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USERNAME=%PROXY_USER% ^
  -e PROXY_PASSWORD=%PROXY_PASS%
goto :finish

:extreme
echo.
echo Running Extreme Test...
echo WARNING: This may put heavy load on your API!
set /p confirm=Are you sure? (y/N): 
if /i not "%confirm%"=="y" goto :end

echo Config: 100 users, 2 minutes
%K6_PATH% run examples\proxy-test.js ^
  --out json=extreme_test.json ^
  --summary-export=extreme_summary.json ^
  -e TEST_URL=%API_URL% ^
  -e VUS=100 ^
  -e DURATION=2m ^
  -e SCENARIO=simple ^
  -e PROXY_HOST=%PROXY_HOST% ^
  -e PROXY_PORT=%PROXY_PORT% ^
  -e PROXY_USERNAME=%PROXY_USER% ^
  -e PROXY_PASSWORD=%PROXY_PASS%
goto :finish

:finish
echo.
echo Test completed!
echo Result files have been generated
echo Run generate-report.bat to create HTML report
echo.
pause

:end
