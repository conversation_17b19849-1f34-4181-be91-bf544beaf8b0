@echo off
title Proxy Connection Checker

echo ========================================
echo SOCKS5 Proxy Connection Checker
echo ========================================
echo.

REM Proxy configuration
set PROXY_HOST=isp.decodo.com
set PROXY_PORT=10001
set PROXY_USER=spo5nyyeb6
set PROXY_PASS=j6IJjtrro64Fie=l2W
set K6_PATH=C:\ProgramData\chocolatey\bin\k6.exe

echo [INFO] Proxy Configuration:
echo Host: %PROXY_HOST%
echo Port: %PROXY_PORT%
echo User: %PROXY_USER%
echo Pass: [HIDDEN]
echo ========================================
echo.

echo [STEP 1] Creating proxy test script...

REM Create simple proxy test
echo import http from 'k6/http'; > proxy_simple_check.js
echo import { check } from 'k6'; >> proxy_simple_check.js
echo. >> proxy_simple_check.js
echo export let options = { >> proxy_simple_check.js
echo   vus: 1, >> proxy_simple_check.js
echo   duration: '10s', >> proxy_simple_check.js
echo }; >> proxy_simple_check.js
echo. >> proxy_simple_check.js
echo export default function() { >> proxy_simple_check.js
echo   const proxyUrl = 'socks5://%PROXY_USER%:%PROXY_PASS%@%PROXY_HOST%:%PROXY_PORT%'; >> proxy_simple_check.js
echo   console.log('Testing proxy connection...'); >> proxy_simple_check.js
echo. >> proxy_simple_check.js
echo   const params = { >> proxy_simple_check.js
echo     proxy: proxyUrl, >> proxy_simple_check.js
echo     timeout: '30s' >> proxy_simple_check.js
echo   }; >> proxy_simple_check.js
echo. >> proxy_simple_check.js
echo   console.log('Connecting to test server...'); >> proxy_simple_check.js
echo   const response = http.get('https://httpbin.org/ip', params); >> proxy_simple_check.js
echo. >> proxy_simple_check.js
echo   const success = check(response, { >> proxy_simple_check.js
echo     'Proxy connection successful': (r) =^> r.status === 200, >> proxy_simple_check.js
echo     'Response time reasonable': (r) =^> r.timings.duration ^< 10000, >> proxy_simple_check.js
echo     'Response body exists': (r) =^> r.body && r.body.length ^> 0, >> proxy_simple_check.js
echo   }); >> proxy_simple_check.js
echo. >> proxy_simple_check.js
echo   if (response.status === 200) { >> proxy_simple_check.js
echo     console.log('SUCCESS: Proxy is working!'); >> proxy_simple_check.js
echo     console.log('Status: ' + response.status); >> proxy_simple_check.js
echo     console.log('Response time: ' + response.timings.duration + 'ms'); >> proxy_simple_check.js
echo     console.log('Response size: ' + response.body.length + ' bytes'); >> proxy_simple_check.js
echo     try { >> proxy_simple_check.js
echo       const data = JSON.parse(response.body); >> proxy_simple_check.js
echo       console.log('Your IP through proxy: ' + data.origin); >> proxy_simple_check.js
echo     } catch (e) { >> proxy_simple_check.js
echo       console.log('Response received but could not parse IP'); >> proxy_simple_check.js
echo     } >> proxy_simple_check.js
echo   } else { >> proxy_simple_check.js
echo     console.log('FAILED: Proxy connection failed!'); >> proxy_simple_check.js
echo     console.log('Status code: ' + response.status); >> proxy_simple_check.js
echo     console.log('Error: ' + response.error); >> proxy_simple_check.js
echo   } >> proxy_simple_check.js
echo. >> proxy_simple_check.js
echo   console.log('=== PROXY TEST SUMMARY ==='); >> proxy_simple_check.js
echo   if (success) { >> proxy_simple_check.js
echo     console.log('RESULT: PROXY IS READY FOR TESTING!'); >> proxy_simple_check.js
echo   } else { >> proxy_simple_check.js
echo     console.log('RESULT: PROXY HAS ISSUES - CHECK CONFIGURATION'); >> proxy_simple_check.js
echo   } >> proxy_simple_check.js
echo } >> proxy_simple_check.js

echo [STEP 2] Running proxy test...
echo ========================================
echo.

"%K6_PATH%" run proxy_simple_check.js

echo.
echo ========================================
echo [STEP 3] Test Complete
echo ========================================
echo.
echo If you see "PROXY IS READY FOR TESTING!" above,
echo your proxy is working correctly.
echo.
echo If you see "PROXY HAS ISSUES", check:
echo - Proxy server address and port
echo - Username and password  
echo - Network connectivity
echo.

echo [INFO] Cleaning up...
del proxy_simple_check.js 2>nul

echo.
pause
